<?php

defined('BASEPATH') OR exit('No direct script access allowed');

	class Reports_model extends CI_Model {
    private $yearId;
    private $current_branch;
    public function __construct() {
      parent::__construct();
      $this->yearId = $this->acad_year->getAcadYearID();
      $this->current_branch = $this->authorization->getCurrentBranch();
  	}

    public function get_blueprints_selection($feetypeId)
    {
      $this->db_readonly->select('*');
      $this->db_readonly->from('feev2_blueprint');
      $this->db_readonly->where('id',$feetypeId);
      return $this->db_readonly->get()->row();
    }

    public function get_blueprint_comp($blueprintId){
      return $this->db_readonly->where('feev2_blueprint_id',$blueprintId)->get('feev2_blueprint_components')->result();
    }

    public function insert_blueprint_report_filter($input){

      $data = array(
        'select_filter' => json_encode($this->input->post('select_filter')),
        'select_columns' => json_encode($this->input->post('select_columns')),
      );
      $this->db->where('id',$input['blueprint_id']);
      return $this->db->update('feev2_blueprint', $data);

    }

    public function get_selected_filters($blueprint_id){
      $result = $this->db_readonly->select('select_filter')->where('id',$blueprint_id)->get('feev2_blueprint')->row();
      if (empty($result) || empty($result->select_filter)) {
        return array();
      }
      return json_decode($result->select_filter);
    }
    public function get_selected_columns($blueprint_id){
      $result = $this->db_readonly->select('select_columns')->where('id',$blueprint_id)->get('feev2_blueprint')->row();
      if (empty($result) || empty($result->select_columns)) {
        return array();
      }
      return json_decode($result->select_columns);
    }

  	public function get_daily_fee_transcation($fee_type, $classId=0,$admission_type,$paymentModes,$mediumId,$donorsId,$rte_nrteId,$created_byId,$from_date,$to_date, $fild_column, $stopId, $kilometer, $combination)
  	{
   		$fromDate = date('Y-m-d',strtotime($from_date));
    	$toDate =date('Y-m-d',strtotime($to_date));
	    $this->db_readonly->select($fild_column.', ftp.reconciliation_status, ft.id as transId, ftp.bank_name, date_format(ftp.cheque_or_dd_date,"%d-%m-%Y") as cheque_or_dd_date , ftp.cheque_dd_nb_cc_dd_number,sy.picture_url,sd.gender, ft.refund_amount')
      ->from('feev2_transaction ft')
      ->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
      ->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id')
      ->where('fbit.feev2_blueprint_id',$fee_type)
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
	    ->join('student_admission sd','ft.student_id=sd.id')
      // ->where('sd.admission_status','2')
	    ->join('student_year sy',"sd.id=sy.student_admission_id")
      // ->where('sy.promotion_status!=','4')
	    ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
	    ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->where('ft.soft_delete !=',1)
	    ->where('ftp.reconciliation_status !=',3)
      ->where('ft.status','SUCCESS')
      // ->where('ftp.reconciliation_status !=',1)
  	  ->order_by('paid_datetime','desc');

        if ($classId) {
          $this->db_readonly->where_in('c.id',$classId);
        }
        if ($admission_type) {
          $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        if ($mediumId) {
          $this->db_readonly->where('sy.medium',$mediumId);
        }
        if ($paymentModes) {
          $this->db_readonly->where('ftp.payment_type',$paymentModes);
        }
        if ($donorsId) {
          $this->db_readonly->where('sy.donor',$donorsId);
        }
        if ($stopId) {
          $this->db_readonly->where('sy.stop',$stopId);
        }
        if ($kilometer) {
          $this->db_readonly->where('sy.has_transport_km',$kilometer);
        }
        if ($combination) {
          $this->db_readonly->where('sy.combination',$combination);
        }
        if ($rte_nrteId) {
          $this->db_readonly->where('sy.is_rte',$rte_nrteId);
        }
        if ($created_byId) {
          $this->db_readonly->where('ftp.receipt_created_by',$created_byId);
        }
        if ($fromDate && $toDate) {
          $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
        }
		  $result = $this->db_readonly->get()->result();

      $transIds = [];
      foreach ($result as $key => $value) {
        $transIds[] = $value->transId;
      }
      if(!empty($transIds)){
        $components = $this->db_readonly->select('ftic.fee_transaction_id,fi.name as installment_name, fbc.name as component_name, format(ftic.amount_paid,2,"EN_IN") as component_amount_paid, ftic.blueprint_installments_id as insId')
        ->from('feev2_transaction_installment_component ftic')
        ->where_in('ftic.fee_transaction_id', $transIds)
        ->join('feev2_installments fi', 'ftic.blueprint_installments_id=fi.id')
        ->join('feev2_blueprint_components fbc', 'ftic.blueprint_component_id=fbc.id')
        ->order_by('ftic.id')
        ->get()->result();
      }

      foreach ($result as  &$res) {
        $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
        if($res->gender == 'M'){
          $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
        }

        if($res->picture_url != '' && $res->picture_url != null) {
          $picUrl = $this->filemanager->getFilePath($res->picture_url);
        }
        $compString = '';
        foreach ($components as $key => $comp) {
          if ($res->transId == $comp->fee_transaction_id) {
            if (!empty($compString))
              $compString .= '<br>';
              $compString .= $comp->component_name.'='.$comp->component_amount_paid;
          }
        }
        $res->components = $compString;
        if (!empty($res->student_name)) {
          $res->student_name = "<img width='30px' height='30px' class='img-circle' src=".$picUrl.">&nbsp;&nbsp;".$res->student_name."";
        }

      }
      return $result;
    }

    public function get_fee_summary_student_list($payment_status, $fee_type, $classId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId,$acad_year_id, $combination)
    {
      if ($donorsId === 'Null') {
        $donorsId = 1;
      }else{
        $donorsId = $donorsId;
      }

      $this->db_readonly->select("sd.id as stdId")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$fee_type)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('student_admission sd','fcs.student_id=sd.id')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      // ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->where('sy.acad_year_id',$this->yearId);
      // ->where('sd.admission_status','2')
      // ->where('sy.promotion_status!=','4')
      if ($acad_year_id) {
          $this->db_readonly->where_in('sd.admission_acad_year_id',$acad_year_id);
        }
        if ($classId) {
          $this->db_readonly->where_in('cs.class_id',$classId);
          // $this->db_readonly->where_in('c.id',$classId);
        }
        if ($classSectionId) {
          $this->db_readonly->where_in('cs.id',$classSectionId);
        }
        if ($admission_type) {
          $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        if ($combination) {
          $this->db_readonly->where('sy.combination',$combination);
        }
        if ($mediumId) {
          $this->db_readonly->where('sy.medium',$mediumId);
        }
        if ($category) {
          $this->db_readonly->where('sd.category',$category);
        }
        if ($rte_nrteId) {
          $this->db_readonly->where('sy.is_rte',$rte_nrteId);
        }
        if ($donorsId) {
          if ($donorsId == 1) {
            $this->db_readonly->where('sy.donor is null');
          }else{
            $this->db_readonly->where('sy.donor',$donorsId);
          }
        }
        if ($payment_status) {
          $this->db_readonly->where('fss.payment_status',$payment_status);
        }
        // if ($created_byId) {
          // $this->db_readonly->where('ftp.receipt_created_by',$created_byId);
        // }

      $result = $this->db_readonly->get()->result();
      $stdIds = [];
      foreach ($result as $key => $res) {
        array_push($stdIds, $res->stdId);
      }
      return $stdIds;
    }

    public function get_fee_summary_student_id_wise($student_ids, $payment_status, $fee_type)
    {

      $this->db_readonly->select("sd.id as stdId, fss.id as schid, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, sd.admission_no, format(fss.total_fee,2,'EN_IN') as fee_amount,  format(ifnull(fss.total_fee_paid,0),2,'EN_IN') as paid_amount, fss.payment_status, format(fss.discount,2,'EN_IN'),  format(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0),2,'EN_IN') as balance,  format((ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)),2,'EN_IN') as concession, format(ifnull(fss.total_fine_amount_paid,0),2,'EN_IN') as fine_amount, format(ifnull(fss.total_card_charge_amount,0),2,'EN_IN') as card_charge_amount,p.first_name as father_name, p.mobile_no as father_mobile_no, sd.sts_number, sy.picture_url, sd.gender, sd.admission_acad_year_id, fss.refund_amount, format(fss.discount,2,'EN_IN') as discount_amount")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$fee_type)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('student_admission sd','fcs.student_id=sd.id')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->join('student_relation sr',"sr.std_id=sd.id and relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      // ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->where('sy.acad_year_id',$this->yearId)
      ->where_in('sd.id', $student_ids);
      // ->where('sd.admission_status','2')
      // ->where('sy.promotion_status!=','4')

      if ($payment_status) {
        $this->db_readonly->where('fss.payment_status',$payment_status);
      }
      // if ($created_byId) {
        // $this->db_readonly->where('ftp.receipt_created_by',$created_byId);
      // }

      $result = $this->db_readonly->get()->result();

      if (empty($result)) {
        return $result;
      }
      $shcIds = [];
      foreach ($result as $key => $value) {
        $shcIds[] = $value->schid;
      }


      $transcation = $this->db_readonly->select("ft.id as transid, student_id, receipt_number, amount_paid, date_format(paid_datetime,'%d-%m-%Y') as paid_date, concession_amount,ftp.payment_type")
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id and reconciliation_status !=3 and reconciliation_status !=1')
      ->where('ft.soft_delete!=',1)
      ->where_in('ft.fee_student_schedule_id',$shcIds)
      ->where('status','SUCCESS')
      ->get()->result();

      $components = $this->db_readonly->select('fsi.fee_student_schedule_id, fsi.installment_amount_paid, fsi.status, (ifnull(fsic.component_amount_paid,0)) as component_amount_paid,  fi.name as installment_name, fbc.name as component_name')
      ->from('feev2_student_installments fsi')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->where_in('fsi.fee_student_schedule_id', $shcIds)
      ->join('feev2_installments fi', 'fsi.feev2_installments_id=fi.id')
      ->join('feev2_blueprint_components fbc', 'fsic.blueprint_component_id=fbc.id')
      ->order_by('fsi.feev2_installments_id','asc')
      ->get()->result();

      foreach ($result as $key => &$res) {
        $transString = '<table  style="margin: 0;" class="table table-bordered">';
        foreach ($transcation as $key => $tran) {
          if ($res->stdId == $tran->student_id) {
            $paymentValue = $this->_get_PaymentValue($tran->payment_type);
            $transString .= '<tr>';
            $transString .= '<td width="30%">'.$tran->paid_date.'</td>';
            $transString .= '<td width="30%">'.$tran->receipt_number.'</td>';
            $transString .= '<td width="20%">'.numberToCurrency_withoutSymbol($tran->amount_paid).'</td>';
            $transString .= '<td width="20%">'.numberToCurrency_withoutSymbol($tran->concession_amount).'</td>';
            $transString .= '<td width="30%">'.$paymentValue.'</td>';
            $transString .= '</tr>';
          }
        }
        $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
        if($res->gender == 'M'){
          $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
        }

        if($res->picture_url != '' && $res->picture_url != null) {
          $picUrl = $this->filemanager->getFilePath($res->picture_url);
        }
          $res->academic_year_of_joining = '';
        if (!empty($res->admission_acad_year_id)) {
          $res->academic_year_of_joining = $this->acad_year->getAcadYearById($res->admission_acad_year_id);
        }
        $transString .= '</table>';
        $res->transaction = $transString;
        $compString = '';
        foreach ($components as $key => $comp) {
          if ($res->schid == $comp->fee_student_schedule_id) {
            $compString .= '<br>';
            // $compString .= $comp->installment_name.' - ';
            if (!empty($compString))
              $compString .= $comp->component_name.'='.numberToCurrency_withoutSymbol($comp->component_amount_paid);
          }
        }
        $res->components = $compString;
        if (!empty($res->student_name)) {
          $res->student_name = "<img width='30px' height='30px' class='img-circle' src=".$picUrl.">&nbsp;&nbsp;".$res->student_name."";

        }
      }
      return $result;

    }

    public function get_fee_summary_student_wise($payment_status, $fee_type, $classId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId,$acad_year_id, $combination)
    {
      if ($donorsId === 'Null') {
        $donorsId = 1;
      }else{
        $donorsId = $donorsId;
      }

      $this->db_readonly->select("sd.id as stdId, fss.id as schid, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, sd.admission_no, format(fss.total_fee,2,'EN_IN') as fee_amount,  format(ifnull(fss.total_fee_paid,0),2,'EN_IN') as paid_amount, fss.payment_status, format(fss.discount,2,'EN_IN'),  format(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0),2,'EN_IN') as balance,  format((ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)),2,'EN_IN') as concession, format(ifnull(fss.total_fine_amount_paid,0),2,'EN_IN') as fine_amount, format(ifnull(fss.total_card_charge_amount,0),2,'EN_IN') as card_charge_amount,p.first_name as father_name, p.mobile_no as father_mobile_no, sd.sts_number, sy.picture_url, sd.gender, sd.admission_acad_year_id, fss.refund_amount, format(fss.discount,2,'EN_IN') as discount_amount")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$fee_type)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('student_admission sd','fcs.student_id=sd.id')
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->join('student_relation sr',"sr.std_id=sd.id and relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      ->where('sy.acad_year_id',$this->yearId)
      // ->where('sd.admission_status','2')
      // ->where('sy.promotion_status!=','4')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left');
      if ($acad_year_id) {
          $this->db_readonly->where_in('sd.admission_acad_year_id',$acad_year_id);
        }
        if ($classId) {
          $this->db_readonly->where_in('c.id',$classId);
        }
        if ($classSectionId) {
          $this->db_readonly->where_in('cs.id',$classSectionId);
        }
        if ($admission_type) {
          $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        if ($combination) {
          $this->db_readonly->where('sy.combination',$combination);
        }
        if ($mediumId) {
          $this->db_readonly->where('sy.medium',$mediumId);
        }
        if ($category) {
          $this->db_readonly->where('sd.category',$category);
        }
        if ($rte_nrteId) {
          $this->db_readonly->where('sy.is_rte',$rte_nrteId);
        }
        if ($donorsId) {
          if ($donorsId == 1) {
            $this->db_readonly->where('sy.donor is null');
          }else{
            $this->db_readonly->where('sy.donor',$donorsId);
          }
        }
        if ($payment_status) {
          $this->db_readonly->where('fss.payment_status',$payment_status);
        }
        if ($created_byId) {
          $this->db_readonly->where('ftp.receipt_created_by',$created_byId);
        }


      $result = $this->db_readonly->get()->result();

      if (empty($result)) {
        return $result;
      }
      $shcIds = [];
      foreach ($result as $key => $value) {
        $shcIds[] = $value->schid;
      }


      $transcation = $this->db_readonly->select("ft.id as transid, student_id, amount_paid, date_format(paid_datetime,'%d-%m-%Y') as paid_date, concession_amount,ftp.payment_type")
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id and reconciliation_status !=3 and reconciliation_status !=1')
      ->where('ft.soft_delete!=',1)
      ->where_in('ft.fee_student_schedule_id',$shcIds)
      ->where('status','SUCCESS')
      ->get()->result();

      $components = $this->db_readonly->select('fsi.fee_student_schedule_id, fsi.installment_amount_paid, fsi.status, (ifnull(fsic.component_amount_paid,0)) as component_amount_paid,  fi.name as installment_name, fbc.name as component_name, ')
      ->from('feev2_student_installments fsi')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->where_in('fsi.fee_student_schedule_id', $shcIds)
      ->join('feev2_installments fi', 'fsi.feev2_installments_id=fi.id')
      ->join('feev2_blueprint_components fbc', 'fsic.blueprint_component_id=fbc.id')
      ->order_by('fsi.feev2_installments_id','asc')
      ->get()->result();

      foreach ($result as $key => &$res) {
        $transString = '<table  style="margin: 0;" class="table table-bordered">';
        foreach ($transcation as $key => $tran) {
          if ($res->stdId == $tran->student_id) {
            $paymentValue = $this->_get_PaymentValue($tran->payment_type);
            $transString .= '<tr>';
            $transString .= '<td width="30%">'.$tran->paid_date.'</td>';
            $transString .= '<td width="20%">'.numberToCurrency_withoutSymbol($tran->amount_paid).'</td>';
            $transString .= '<td width="20%">'.numberToCurrency_withoutSymbol($tran->concession_amount).'</td>';
            $transString .= '<td width="30%">'.$paymentValue.'</td>';
            $transString .= '</tr>';
          }
        }
        $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/femalestu.png';
        if($res->gender == 'M'){
          $picUrl = 'https://nextelement-prodserver-mumbai.s3.ap-south-1.amazonaws.com/nextelement-common/Staff and Admin icons 64px/malestu.png';
        }

        if($res->picture_url != '' && $res->picture_url != null) {
          $picUrl = $this->filemanager->getFilePath($res->picture_url);
        }
          $res->academic_year_of_joining = '';
        if (!empty($res->admission_acad_year_id)) {
          $res->academic_year_of_joining = $this->acad_year->getAcadYearById($res->admission_acad_year_id);
        }
        $transString .= '</table>';
        $res->transaction = $transString;
        $compString = '';
        foreach ($components as $key => $comp) {
          if ($res->schid == $comp->fee_student_schedule_id) {
            $compString .= '<br>';
            // $compString .= $comp->installment_name.' - ';
            if (!empty($compString))
              $compString .= $comp->component_name.'='.numberToCurrency_withoutSymbol($comp->component_amount_paid);
          }
        }
        $res->components = $compString;
        if (!empty($res->student_name)) {
          $res->student_name = "<img width='30px' height='30px' class='img-circle' src=".$picUrl.">&nbsp;&nbsp;".$res->student_name."";

        }
      }
      return $result;

    }

    private function _get_PaymentValue($payment_type){
      switch ($payment_type) {
        case '1':
          $pValue = 'DD';
          break;
        case '2':
          $pValue = 'Credit Card';
          break;
        case '3':
          $pValue = 'Debit Card';
          break;
        case '4':
          $pValue = 'Cheque';
          break;
        case '5':
          $pValue = 'Wallet Payment';
          break;
        case '6':
          $pValue = 'Challan';
          break;
        case '7':
          $pValue = 'Card (POS)';
          break;
        case '8':
          $pValue = 'Net Banking';
          break;
        case '9':
          $pValue = 'Cash';
          break;
        case '10':
          $pValue = 'Online Link';
          break;
        case '11':
          $pValue = 'UPI';
          break;
        default:
           $pValue = '';
          break;
      }
      return $pValue;
    }

    public function get_concession_student_count($classSectionId,$fee_type,$classId){
      $this->db_readonly->select('fcs.id as cohort_student_id, fcs.blueprint_id, fcs.student_id, fss.id as schId, sum(ifnull(fss.total_concession_amount,0)) as total_concession_amount, sum(ifnull(fss.total_concession_amount_paid,0)) as total_concession_amount_paid, payment_status')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->order_by('c.id, cs.id, sa.first_name')
      ->group_by('fcs.id');
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($fee_type) {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $result = $this->db_readonly->get()->result();
      $total_con = 0;
      $studentArry = [];
      foreach ($result as $key => $val) {
        $total_con = $val->total_concession_amount + $val->total_concession_amount_paid;
        if ($total_con != 0) {
          array_push($studentArry, $val->student_id);
        }
      }
      return $studentArry;

    }

    public function get_concession_list_new($student_id, $fee_type, $classSectionId, $classId){
      // echo "<pre>hiiii"; print_r($student_id); die();
      $this->db_readonly->select("fcs.id as cohort_student_id, fcs.blueprint_id, fcs.student_id, fss.id as schId, sum(ifnull(fsi.total_concession_amount,0)) as total_concession_amount, sum(ifnull(fsi.total_concession_amount_paid,0)) as total_concession_amount_paid, payment_status,fbp.name as fee_type_name")
      ->from('feev2_cohort_student fcs')
      ->where_in('fcs.student_id',$student_id)
      ->join('feev2_student_schedule fss',"fcs.id=fss.feev2_cohort_student_id and fss.acad_year_id = $this->yearId")
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_blueprint fbp','fbp.id=fcs.blueprint_id')
      ->group_by('fcs.id');
      if (!empty($fee_type)) {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      $schDetails  = $this->db_readonly->get()->result();
      $cohortStdIds = [];
      // $stdIds = [];
      if (!empty($schDetails)) {
        foreach ($schDetails as $key => $val) {
          array_push($cohortStdIds, $val->cohort_student_id);
          // array_push($stdIds, $val->student_id);
        }
      }
      $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as  stdName,  concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name, sa.admission_no, concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as  parent_name, c.id as cId, cs.id as csId, sa.first_name, ifnull(famp.additional_amount_paid,0) as additional_amount_paid, ifnull(sa.enrollment_number,'NA') as enrollment_number, sa.category, sa.caste")
      ->from('student_admission sa')
      ->where_in('sa.id',$student_id)
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('student_relation sr',"sr.std_id=sa.id and sr.relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('feev2_additional_amount_paid famp','sa.id=famp.student_id','left')
      ->order_by('c.id, cs.id, sa.first_name');
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $stdDetails = $this->db_readonly->get()->result();

      $this->db_readonly->select('fcpdc.*')
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
      ->join('feev2_concessiontype2_pre_defined_concession fcpdc','fcs.id=fcpdc.cohort_student_id')
      ->where_in('fcs.student_id',$student_id);

      if(!empty($fee_type)){
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      $concessionType2=$this->db_readonly->get()->result();
      $concession = [];
      if(!empty($cohortStdIds)){
        $concession = $this->db_readonly->select('fc.cohort_student_id, fc.concession_name')
        ->from('feev2_concessions fc')
        ->where_in('fc.cohort_student_id', $cohortStdIds)
        ->get()->result();
      }
    
      foreach ($schDetails as $key => &$std) {
        $filterString = '';
        if (!empty($concession)) {
          foreach ($concession as $key => $con) {
            if ($std->cohort_student_id == $con->cohort_student_id) {
              if (!empty($filterString))
              $filterString = $filterString . ', ';
              $filterString = $filterString . $con->concession_name;
            }
          }
        }

        $conType2 = '';
        if (!empty($concessionType2)) {
          foreach ($concessionType2 as $key => $con) {
            if ($std->cohort_student_id == $con->cohort_student_id) {
              if (!empty($conType2))
              $conType2 = $filterString . ', ';
              $conType2 = $filterString . $con->remarks;
            }
          }
        }

        foreach ($stdDetails as $key => $val) {
          if ($std->student_id == $val->stdId) {
            $std->stdName = $val->stdName;
            $std->stdId = $val->stdId;
            $std->class_name = $val->class_name;
            $std->cId = $val->cId;
            $std->csId = $val->csId;
            $std->first_name = $val->first_name;
            $std->admission_no = $val->admission_no;
            $std->enrollment_number = $val->enrollment_number;
            $std->parent_name = $val->parent_name;
            $std->category =(!empty($this->settings->getSetting('category')[$val->category])) ? $this->settings->getSetting('category')[$val->category] : 'NA';
            $std->caste =  (!empty($val->caste)) ? $val->caste : 'NA';
            $std->additional_amount_paid = $val->additional_amount_paid;
          }
        }
        $std->concession_remarks = $filterString;
        $std->contype2_remarks = $conType2;
      }

      array_multisort(array_column($schDetails, 'cId'), SORT_ASC, array_column($schDetails, 'csId'), SORT_ASC, array_column($schDetails, 'first_name'), SORT_ASC, $schDetails);

      return $schDetails;
    }
    public function get_concession_day_list_new($fee_type, $classSectionId, $classId,$from_date,$to_date){
      // echo "<pre>hiiii  "; print_r($from_date);
      // echo "<pre>"; print_r($to_date); die();

      $this->db_readonly->select("concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name ,date_format(fcc.created_on,'%d-%m-%Y') as create_date,
      fcc.remarks,fcc.created_by, concat(ifnull(c.class_name,''), '',ifnull(cs.section_name,'')) as class_section
      ,fb.name as fee_type,fcc.concession_amount")
      ->from('feev2_concessiontype2_pre_defined_concession fcc')
      ->join('feev2_cohort_student fcs','fcc.cohort_student_id=fcs.id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id');
      if(!empty($fee_type)){
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      $this->db_readonly->where("date(fcc.created_on) BETWEEN '$from_date' AND '$to_date'");
      $resulta = $this->db_readonly->get()->result();
      foreach ($resulta as $key => $value) {
        $value->created_name = $this->_getAvatarNameById($value->created_by);
     }
    //  echo "<pre>"; print_r($result); die();
     return $resulta;
    }

    public function get_concession_list ($feeType) {
      $this->db_readonly->select("fcs.id as cohort_student_id, fcs.blueprint_id, fcs.student_id, fss.id as schId, sum(ifnull(fsi.total_concession_amount,0)) as total_concession_amount, sum(ifnull(fsi.total_concession_amount_paid,0)) as total_concession_amount_paid, payment_status, ")
    ->from('feev2_cohort_student fcs')
    ->where('fcs.blueprint_id',$feeType)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->group_by('fcs.id');
    $schDetails  = $this->db_readonly->get()->result();

    $cohortStdIds = [];
    $stdIds = [];
    if (!empty($schDetails)) {
      foreach ($schDetails as $key => $val) {
        array_push($cohortStdIds, $val->cohort_student_id);
        array_push($stdIds, $val->student_id);

      }
      $stdDetails = $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as  stdName,  concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, sa.admission_no, concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as  parent_name")
      ->from('student_admission sa')
      ->where_in('sa.id',$stdIds)
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('student_relation sr',"sr.std_id=sa.id and sr.relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      ->join('class c','c.id=sy.class_id')
      ->join('class_section cs','c.id=sy.class_section_id','left')
      ->get()->result();
      $concession = $this->db_readonly->select('fc.cohort_student_id, fc.concession_name')
      ->from('feev2_concessions fc')
      ->where_in('fc.cohort_student_id', $cohortStdIds)
      ->get()->result();

      foreach ($schDetails as $key => &$std) {
        $filterString = '';
        if (!empty($concession)) {
          foreach ($concession as $key => $con) {
            if ($std->cohort_student_id == $con->cohort_student_id) {
              if (!empty($filterString))
              $filterString = $filterString . ', ';
              $filterString = $filterString . $con->concession_name;

            }
          }
        }
        foreach ($stdDetails as $key => $val) {
          if ($std->student_id == $val->stdId) {
            $std->stdName = $val->stdName;
            $std->class_name = $val->class_name;
            $std->admission_no = $val->admission_no;
            $std->parent_name = $val->parent_name;
          }
        }
        $std->concession_remarks = $filterString;
      }
    }
    return $schDetails;

      // $this->db_readonly->select("fss.id, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as stdName,  concat(cs.class_name, cs.section_name) as class_section, sa.admission_no, total_fee, ifnull(total_concession_amount,0) as total_concession_amount, ifnull( total_concession_amount_paid,0) as total_concession_amount_paid, fc.concession_name")
      // ->from('feev2_cohort_student fcs')
      // ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      // ->join('feev2_concessions fc','fcs.id=fc.cohort_student_id')
      // ->join('student_admission sa','fcs.student_id=sa.id')
      // ->join('student_year sy','sa.id=sy.student_admission_id')
      // ->where('fcs.blueprint_id',$feeType)
      // // ->where('sa.admission_status','2')
      // // ->where('sy.promotion_status!=','4')
      // // ->where('fss.total_concession_amount !=',0)
      // ->join('class_section cs','sy.class_section_id=cs.id')
      // ->join('class c','cs.class_id=c.id')
      // ->order_by('fc.concession_name, cs.id, sa.first_name');
      // if($this->current_branch) {
      //   $this->db_readonly->where('c.branch_id',$this->current_branch);
      // }
      // $this->db_readonly->group_by('sy.student_admission_id');
      // $result =  $this->db_readonly->get()->result();
    }

    public function get_installments_types($selected_blueprint){
      return $this->db_readonly->select('fit.id, fit.name')
      ->from('feev2_blueprint_installment_types fbit')
      ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
      ->where('fbit.feev2_blueprint_id',$selected_blueprint)
      ->get()->result();
    }

    public function get_installamentsby_instype($instypId){
      return $this->db_readonly->select('fi.id, fi.name')
      ->from('feev2_installments fi')
      ->where('fi.feev2_installment_type_id',$instypId)
      ->get()->result();
    }

    public function get_installamentsfee_data_by_instype($clsId, $installment_type, $installmentId, $fee_type, $due_cross, $classSectionId,  $rte_nrte, $student_ids){
       $today = date('Y-m-d');
      $this->db_readonly->select("sa.id, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as stdName,  concat(cs.class_name, cs.section_name) as class_section, sa.admission_no, fsi.installment_amount, fsi.installment_amount_paid, fsi.status, fsi.total_concession_amount, fsi.total_concession_amount_paid, fi.name, fit.name as ins_type_name, date_format(fi.end_date,'%d-%m-%Y') as due_date,  fi.id  as insId, fb.name as blueprint_name, sa.preferred_contact_no, fss.total_fine_amount, fss.discount,sa.gender, sy.picture_url, p1.first_name as father_name, p2.first_name as mother_name, ifnull(p1.mobile_no,'')  as f_number, ifnull(p2.mobile_no,'') as m_number, u1.token as f_token, u2.token as m_token, sa.preferred_parent")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id', $fee_type)
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi',"fss.id=fsi.fee_student_schedule_id and fsi.status != 'FULL'")
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_installment_types fit','fi.feev2_installment_type_id=fit.id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->where_in('sa.id',$student_ids)
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)

      ->join('student_relation sr1', "sr1.std_id=sa.id and sr1.relation_type='Father'")
      ->join('parent p1', 'p1.id=sr1.relation_id')
      ->join('avatar a1', 'p1.id=a1.stakeholder_id')
      ->join('users u1', 'u1.id=a1.user_id')
      ->where('a1.avatar_type', 2)
      ->join('student_relation sr2', "sr2.std_id=sa.id and sr2.relation_type='mother'")
      ->join('parent p2', 'p2.id=sr2.relation_id')
      ->join('avatar a2', 'p2.id=a2.stakeholder_id')
      ->join('users u2', 'u2.id=a2.user_id')
      ->where('a2.avatar_type', 2)
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->order_by('fi.id');
      if ($installmentId) {
        $this->db_readonly->where('fsi.feev2_installments_id',$installmentId);
      }
      if ($clsId) {
        $this->db_readonly->where_in('cs.class_id',$clsId);
      }
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($due_cross) {
        $this->db_readonly->where('fi.end_date <',$today);
      }

       if ($rte_nrte) {
        $this->db_readonly->where('sy.is_rte',$rte_nrte);
      }
      $this->db_readonly->order_by('cs.class_name, cs.section_name, sa.first_name');
      $result = $this->db_readonly->get()->result();

      $ins_data= [];
      foreach ($result as $key => $val) {

        if(!array_key_exists($val->id, $ins_data)) {
          $ins_data[$val->id]['stdName'] = $val->stdName;
          $ins_data[$val->id]['key'] = $key;
          $ins_data[$val->id]['class_section'] = $val->class_section;
          $ins_data[$val->id]['admission_no'] = $val->admission_no;
          $ins_data[$val->id]['father_name'] = $val->father_name;
          $ins_data[$val->id]['mother_name'] = $val->mother_name;
          $ins_data[$val->id]['f_number'] = $val->f_number;
          $ins_data[$val->id]['m_number'] = $val->m_number;
          $ins_data[$val->id]['f_token'] = $val->f_token;
          $ins_data[$val->id]['m_token'] = $val->m_token;
          $ins_data[$val->id]['ins_type_name'] = $val->ins_type_name;
          $ins_data[$val->id]['blueprint'] = $val->blueprint_name;
          $ins_data[$val->id]['preferred_number'] = $val->preferred_contact_no;
          $ins_data[$val->id]['preferred_parent'] = $val->preferred_parent;
          $ins_data[$val->id]['std_id'] = $val->id;
          $ins_data[$val->id]['ins'] = '';
          $ins_data[$val->id]['message'] = '';
          $ins_data[$val->id]['balance'] = 0;
          $ins_data[$val->id]['concession'] = 0;
          $ins_data[$val->id]['fine_amount'] = 0;
          $ins_data[$val->id]['discount_amount'] = 0;
          $ins_data[$val->id]['ins_amount_paid'] = '';
          $ins_data[$val->id]['ins_bal_amount'] = '';
        }
        $ins_data[$val->id]['ins'] .= $val->name.': '.numberToCurrency_withoutSymbol($val->installment_amount).'<br>';
        $ins_data[$val->id]['ins_amount_paid'] .= $val->name.': '.numberToCurrency_withoutSymbol($val->installment_amount_paid).'<br>';
        $ins_data[$val->id]['message'] .= $val->name.': '.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->total_concession_amount - $val->total_concession_amount_paid). ' (Due Date: ' . $val->due_date . '). ';
        $ins_data[$val->id]['ins_bal_amount'] .= $val->name.' Rs.'.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->total_concession_amount - $val->total_concession_amount_paid).' ';

        $ins_data[$val->id]['balance'] += $val->installment_amount - $val->installment_amount_paid - $val->total_concession_amount - $val->total_concession_amount_paid;
        $ins_data[$val->id]['concession'] += ($val->total_concession_amount + $val->total_concession_amount_paid);
        $ins_data[$val->id]['fine_amount'] += $val->total_fine_amount;
        $ins_data[$val->id]['discount_amount'] += $val->discount;

      }
      usort($ins_data, function($a, $b) { return $a['key'] - $b['key']; });
      return $ins_data;
    }
    public function get_installamentsfee_data_by_instype_sms($clsId, $installment_type, $installmentId, $fee_type, $due_cross, $classSectionId, $stdIds, $rte_nrte){
       $today = date('Y-m-d');
      $this->db_readonly->select("sa.id, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as stdName,  concat(cs.class_name, cs.section_name) as class_section, sa.admission_no, fsi.installment_amount, fsi.installment_amount_paid, fsi.status, fsi.total_concession_amount, fsi.total_concession_amount_paid, fi.name, fit.name as ins_type_name, date_format(fi.end_date,'%d-%m-%Y') as due_date,  fi.id  as insId, fb.name as blueprint_name, sa.preferred_contact_no, fss.total_fine_amount, fss.discount,sa.gender, sy.picture_url, p1.first_name as father_name, p2.first_name as mother_name, ifnull(p1.mobile_no,'')  as f_number, ifnull(p2.mobile_no,'') as m_number, u1.token as f_token, u2.token as m_token, sa.preferred_parent")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id', $fee_type)
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi',"fss.id=fsi.fee_student_schedule_id and fsi.status != 'FULL'")
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_installment_types fit','fi.feev2_installment_type_id=fit.id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('student_relation sr1', "sr1.std_id=sa.id and sr1.relation_type='Father'")
      ->join('parent p1', 'p1.id=sr1.relation_id')
      ->join('avatar a1', 'p1.id=a1.stakeholder_id')
      ->join('users u1', 'u1.id=a1.user_id')
      ->where('a1.avatar_type', 2)
      ->join('student_relation sr2', "sr2.std_id=sa.id and sr2.relation_type='mother'")
      ->join('parent p2', 'p2.id=sr2.relation_id')
      ->join('avatar a2', 'p2.id=a2.stakeholder_id')
      ->join('users u2', 'u2.id=a2.user_id')
      ->where('a2.avatar_type', 2)
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->order_by('fi.id');
      if ($installmentId) {
        $this->db_readonly->where('fsi.feev2_installments_id',$installmentId);
      }
      if ($clsId) {
        $this->db_readonly->where_in('cs.class_id',$clsId);
      }
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($due_cross) {
        $this->db_readonly->where('fi.end_date <',$today);
      }
      if ($stdIds) {
        $this->db_readonly->where_in('sa.id',$stdIds);
      }
       if ($rte_nrte) {
        $this->db_readonly->where('sy.is_rte',$rte_nrte);
      }
      $this->db_readonly->order_by('cs.class_name, cs.section_name, sa.first_name');
      $result = $this->db_readonly->get()->result();

      $ins_data= [];
      foreach ($result as $key => $val) {

        if(!array_key_exists($val->id, $ins_data)) {
          $ins_data[$val->id]['stdName'] = $val->stdName;
          $ins_data[$val->id]['key'] = $key;
          $ins_data[$val->id]['class_section'] = $val->class_section;
          $ins_data[$val->id]['admission_no'] = $val->admission_no;
          $ins_data[$val->id]['father_name'] = $val->father_name;
          $ins_data[$val->id]['mother_name'] = $val->mother_name;
          $ins_data[$val->id]['f_number'] = $val->f_number;
          $ins_data[$val->id]['m_number'] = $val->m_number;
          $ins_data[$val->id]['f_token'] = $val->f_token;
          $ins_data[$val->id]['m_token'] = $val->m_token;
          $ins_data[$val->id]['ins_type_name'] = $val->ins_type_name;
          $ins_data[$val->id]['blueprint'] = $val->blueprint_name;
          $ins_data[$val->id]['preferred_number'] = $val->preferred_contact_no;
          $ins_data[$val->id]['preferred_parent'] = $val->preferred_parent;
          $ins_data[$val->id]['std_id'] = $val->id;
          $ins_data[$val->id]['ins'] = '';
          $ins_data[$val->id]['message'] = '';
          $ins_data[$val->id]['balance'] = 0;
          $ins_data[$val->id]['concession'] = 0;
          $ins_data[$val->id]['fine_amount'] = 0;
          $ins_data[$val->id]['discount_amount'] = 0;
          $ins_data[$val->id]['ins_amount_paid'] = '';
          $ins_data[$val->id]['ins_bal_amount'] = '';
        }
        $ins_data[$val->id]['ins'] .= $val->name.': '.numberToCurrency_withoutSymbol($val->installment_amount).'<br>';
        $ins_data[$val->id]['ins_amount_paid'] .= $val->name.': '.numberToCurrency_withoutSymbol($val->installment_amount_paid).'<br>';
        $ins_data[$val->id]['message'] .= $val->name.': '.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->total_concession_amount - $val->total_concession_amount_paid). ' (Due Date: ' . $val->due_date . '). ';
        $ins_data[$val->id]['ins_bal_amount'] .= $val->name.' Rs.'.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->total_concession_amount - $val->total_concession_amount_paid).' ';
        $ins_data[$val->id]['balance'] += $val->installment_amount - $val->installment_amount_paid - $val->total_concession_amount - $val->total_concession_amount_paid;
        $ins_data[$val->id]['concession'] += ($val->total_concession_amount + $val->total_concession_amount_paid);
        $ins_data[$val->id]['fine_amount'] += $val->total_fine_amount;
        $ins_data[$val->id]['discount_amount'] += $val->discount;

      }
      usort($ins_data, function($a, $b) { return $a['key'] - $b['key']; });
      return $ins_data;
    }
    public function get_installamentsfee_data_by_instype_student_length($clsId, $installment_type, $installmentId, $fee_type, $due_cross, $classSectionId, $rte_nrte){
      $today = date('Y-m-d');
      $this->db_readonly->select("sa.id as stdId")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id', $fee_type)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi',"fss.id=fsi.fee_student_schedule_id and fsi.status != 'FULL'")
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id')
      ->group_by('fcs.student_id')
      ->order_by('fi.id');
      if ($installmentId) {
        $this->db_readonly->where('fsi.feev2_installments_id',$installmentId);
      }
      if ($clsId) {
        $this->db_readonly->where_in('cs.class_id',$clsId);
      }
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($due_cross) {
        $this->db_readonly->where('fi.end_date <',$today);
      }
      if ($rte_nrte) {
        $this->db_readonly->where('sy.is_rte',$rte_nrte);
      }
      $this->db_readonly->order_by('cs.class_name, cs.section_name, sa.first_name');
      $result = $this->db_readonly->get()->result();
      $stdIds = [];
      foreach ($result as $key => $res) {
        array_push($stdIds, $res->stdId);
      }
      return $stdIds;
    }

    public function get_all_dates_transcation(){

      $this->db_readonly->distinct();
      return $this->db_readonly->select("date_format(paid_datetime,'%d-%m-%Y') as paid_date")
        ->from('feev2_transaction')
        ->where('status','SUCCESS')
        ->order_by('paid_datetime','desc')
        ->get()->result();
    }

    public function get_excess_amount_details($class_id, $excess_payment_type) {
      $this->db_readonly->select("concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as cs_name, faa.total_amount, faa.total_used_amount, date_format(faa.created_on,'%d-%m-%Y') as created_date, concat(ifnull(sm.first_name,' '), ' ', ifnull(sm.last_name,' ')) as created_staff_name, faa.remarks, ifnull(faa.excess_refund_amount,0) as refund_amount, ifnull(faa.receipt_number,'') as receipt_number, faa.payment_type")
        ->from('feev2_additional_amount faa')
        ->join('student_admission sa', 'sa.id=faa.student_id')
        ->join('student_year sy', "sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId")
        ->join('staff_master sm', 'sm.id=faa.created_by', 'left')
        ->join('class c', 'sy.class_id=c.id')
        ->join('class_section cs', 'cs.id=sy.class_section_id','left')
        ->order_by('faa.id', 'desc');
        if($class_id){
          $this->db_readonly->where_in('c.id',$class_id);
        }
        if($excess_payment_type){
          $this->db_readonly->where_in('faa.payment_type',$excess_payment_type);
        }
      $result = $this->db_readonly->get()->result();
      return $result;
    }

    public function getChallanAmountforDaywise($date){
      return $this->db_readonly->select("sum(amount_paid) as challanAmount, date_format(recon_created_on,'%d-%m-%Y') as date")
      ->from('feev2_transaction ft')
      ->where('ft.status','SUCCESS')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('date_format(recon_created_on,"%d-%m-%Y")',$date)
      ->where('soft_delete !=',-1)
      ->where('reconciliation_status !=',3)
      ->get()->row();
    }

    public function getBankAmountforDaywise($date){
      return $this->db_readonly->select("sum(amount_paid) as bankAmount,date_format(recon_submitted_on,'%d-%m-%Y') as date")
      ->from('feev2_transaction ft')
      ->where('ft.status','SUCCESS')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('date_format(recon_submitted_on,"%d-%m-%Y")',$date)
      ->where('soft_delete !=',-1)
      ->where('reconciliation_status !=',3)
      ->get()->row();
    }

    public function get_reconciled_details($fee_type_id, $reconcilation, $from_date, $to_date){

      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("ft.id as trnsId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as stdName,  concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section, sa.admission_no, ft.concession_amount, ft.amount_paid, date_format(ft.paid_datetime,'%d-%m-%Y') as paid_date, ft.receipt_number, ftp.payment_type, allowed_payment_modes, bank_name, bank_branch, date_format(cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date , cheque_dd_nb_cc_dd_number, reconciliation_status, fcs.id as chortStudent_id, ft.fine_amount")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$fee_type_id)
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->join('student_admission sa','ft.student_id=sa.id')
      // ->where('sa.admission_status','2')
      // ->where('sy.promotion_status!=','4')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left');
      if($reconcilation){
        $this->db_readonly->where('ftp.reconciliation_status',$reconcilation);
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->order_by('sa.id');
      $result = $this->db_readonly->get()->result();

      foreach ($result as $key => &$val) {
        $json_decode = json_decode($val->allowed_payment_modes);
        foreach ($json_decode as $key => $decode_value) {
          if ($val->payment_type == $decode_value->value) {
            $val->payment_type = $decode_value->name;
          }
        }
      }
      return $result;

    }

    public function get_class_wise_summary($blueprint_id, $category, $componentId,$classSectionId){
      if ($classSectionId) {
       $sql = "concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, cs.id as clssId";
      }else{
        $sql = "c.class_name as class_name, c.id as clssId";
      }

     $this->db_readonly->select($sql.", sum(fsic.component_amount) as totalFee, sum(ifnull(fsic.component_amount_paid,0)) as total_collected_amount, sum(ifnull(fsic.concession_amount,0) + ifnull(fsic.concession_amount_paid,0)) as concession, sum(fsic.component_amount - ifnull(fsic.component_amount_paid,0) - ifnull(fsic.concession_amount,0) - ifnull(fsic.concession_amount_paid,0)) as balance, fsic.id as fsiD, sum(ifnull(fsic.refund_amount,0)) as refund_amount")
      ->from('feev2_student_installments_components fsic')
      ->join('feev2_student_installments fsi','fsi.id=fsic.fee_student_installment_id')
      ->join('feev2_student_schedule fss','fsi.fee_student_schedule_id=fss.id')
      ->join('feev2_cohort_student fcs',"fcs.id=fss.feev2_cohort_student_id and fcs.blueprint_id=$blueprint_id")
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      // ->where('sy.promotion_status!=','4')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left');
      if ($category !=0) {
        $this->db_readonly->where('sa.category',$category);
      }
      if ($componentId !=0) {
        $this->db_readonly->where('fsic.blueprint_component_id',$componentId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      if ($classSectionId) {
        $this->db_readonly->group_by('cs.id');
        $this->db_readonly->order_by('cs.id');
      }else{
        $this->db_readonly->group_by('c.id');
        $this->db_readonly->order_by('c.id');
      }
      $transCount = $this->db_readonly->get()->result();

      if ($classSectionId) {
       $sql1 = "concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, cs.id as clssId";
      }else{
        $sql1 = "c.class_name as class_name, c.id as clssId";
      }

      $this->db_readonly->select($sql1.", count(sa.id) as stdCount")
      // ->join('feev2_cohort_student fcs',"fcs.id=fss.feev2_cohort_student_id and fcs.blueprint_id=$blueprint_id")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id', $blueprint_id)
      ->join('student_admission sa','fcs.student_id=sa.id')
      // ->where('sa.admission_status','2')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
       // ->where('sy.promotion_status!=','4')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id');
       if ($classSectionId) {
        $this->db_readonly->group_by('cs.id');
        $this->db_readonly->order_by('cs.id');
      }else{
        $this->db_readonly->group_by('c.id');
        $this->db_readonly->order_by('c.id');
      }
      $stdCount =  $this->db_readonly->get()->result();
      foreach ($transCount as $key => &$val) {
        foreach ($stdCount as $key => $std) {
          if ($val->clssId == $std->clssId) {
            $val->student_count = $std->stdCount;
          }
        }
      }
      return $transCount;
    }

    public function get_fee_class_summary_student_count($feeType){
      // If feeType is an array, handle multiple fee types
      if (is_array($feeType)) {
        $this->db_readonly->select('fcs.student_id, fcs.blueprint_id');
        $this->db_readonly->from('feev2_cohort_student fcs');
        $this->db_readonly->where_in('fcs.blueprint_id', $feeType);
      } else {
        $this->db_readonly->select('fcs.student_id, fcs.blueprint_id');
        $this->db_readonly->from('feev2_cohort_student fcs');
        $this->db_readonly->where('fcs.blueprint_id', $feeType);
      }
      $result = $this->db_readonly->get()->result();

      $stdIDs = [];
      $feeTypeAssignments = [];

      foreach ($result as $key => $val) {
        array_push($stdIDs, $val->student_id);
        // Track which student is assigned to which fee type
        if (!isset($feeTypeAssignments[$val->blueprint_id])) {
          $feeTypeAssignments[$val->blueprint_id] = [];
        }
        $feeTypeAssignments[$val->blueprint_id][] = $val->student_id;
      }

      // Get all classes with their student counts
      $this->db_readonly->select('c.id as classId, c.class_name, COUNT(DISTINCT sa.id) as total_student_count');
      $this->db_readonly->from('student_admission sa');
      $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id');
      $this->db_readonly->where('sy.acad_year_id', $this->yearId);
      $this->db_readonly->where('sy.promotion_status!=','JOINED');
      $this->db_readonly->where('sy.promotion_status!=','4');
      $this->db_readonly->where('sy.promotion_status!=','5');
      $this->db_readonly->where_in('sa.admission_status',['1','2']);
      $this->db_readonly->join('class c', 'sy.class_id=c.id');
      $this->db_readonly->where('c.acad_year_id', $this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id', $this->current_branch);
      }
      $this->db_readonly->group_by('c.id');
      $this->db_readonly->order_by('c.display_order');
      $allClasses = $this->db_readonly->get()->result();
      
      // Now get the students assigned to fee types by class
      $this->db_readonly->select('sa.id as student_id, c.id as classId');
      $this->db_readonly->from('student_admission sa');
      if (!empty($stdIDs)) {
        $this->db_readonly->group_start();
        $stdIDsChunk = array_chunk($stdIDs, 200);
        foreach($stdIDsChunk as $std) {
          $this->db_readonly->or_where_in('sa.id', $std);
        }
        $this->db_readonly->group_end();
      }
      $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id');
      $this->db_readonly->where('sy.acad_year_id', $this->yearId);
      $this->db_readonly->join('class c', 'sy.class_id=c.id');
      $this->db_readonly->where('c.acad_year_id', $this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id', $this->current_branch);
      }
      $this->db_readonly->group_by('c.id, sa.id');
      $this->db_readonly->order_by('c.display_order');
      $assignedStudents = $this->db_readonly->get()->result();

      // Count assigned students by class
      $assignedCounts = [];
      foreach ($assignedStudents as $student) {
        if (!isset($assignedCounts[$student->classId])) {
          $assignedCounts[$student->classId] = 0;
        }
        $assignedCounts[$student->classId]++;
      }

      // Prepare the final result with class IDs and additional data
      $classData = [];
      foreach ($allClasses as $class) {
        $classData[] = [
          'classId' => $class->classId,
          'class_name' => $class->class_name,
          'total_student_count' => $class->total_student_count,
          'assigned_count' => isset($assignedCounts[$class->classId]) ? $assignedCounts[$class->classId] : 0,
          'fee_type_assignments' => $feeTypeAssignments
        ];
      }

      // Return just the class IDs for backward compatibility
      $studentArry = [];
      foreach ($classData as $class) {
        array_push($studentArry, $class['classId']);
      }

      return ['class_ids' => $studentArry, 'class_data' => $classData];
    }

    public function get_fee_class_summary_student_details($class_ids, $fee_type, $classSectionId){
      // Get all students in the specified classes
      $this->db_readonly->select('sa.id as student_id, c.class_name as class_name, c.id as clssId, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status')
      ->from('student_admission sa')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->where('sy.promotion_status!=','JOINED')
      // ->where('sy.promotion_status!=','4')
      // ->where('sy.promotion_status!=','5')
      // ->where_in('sa.admission_status',['1','2'])
      ->where('c.acad_year_id',$this->yearId);

      if (is_array($class_ids) && !empty($class_ids)) {
        $this->db_readonly->where_in('c.id', $class_ids);
      }

      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id', $this->current_branch);
      }

      $studentData = $this->db_readonly->order_by('c.display_order')->get()->result();

      $studentArry = [];
      $studentIds = [];
      $classStudentCounts = [];

      foreach ($studentData as $key => $val) {
        $studentArry[$val->student_id] = $val;
        array_push($studentIds, $val->student_id);
        // Count students per class for total strength
        if(in_array($val->admission_status,['1','2'])){
          if (!isset($classStudentCounts[$val->clssId])) {
            $classStudentCounts[$val->clssId] = 0;
          }
          $classStudentCounts[$val->clssId]++;
        }
      }

      // Handle multiple fee types
      $fee_types = is_array($fee_type) ? $fee_type : [$fee_type];

      // Initialize class array with all classes
      $classArry = [];
      foreach ($class_ids as $cid) {
        if (isset($studentData[0]) && $studentData[0]->clssId == $cid) {
          $class_name = $studentData[0]->class_name;
        } else {
          // Get class name if not already available
          $class = $this->db_readonly->select('class_name')
            ->from('class')
            ->where('id', $cid)
            ->get()->row();
          $class_name = $class ? $class->class_name : 'Unknown';
        }

        $classArry[$cid] = [
          'class_name' => $class_name,
          'total_fee' => 0,
          'total_collected_amount' => 0,
          'concession' => 0,
          'adjustment' => 0,
          'balance' => 0,
          'refund_amount' => 0,
          'student_count' => isset($classStudentCounts[$cid]) ? $classStudentCounts[$cid] : 0,
          'assigned_count' => 0,
          'loan_provider_charges' => 0,
          'fine_amount' => 0,
          'total_fine' => 0,
          'total_fine_waived' => 0,
          'total_fine_amount_paid' => 0,
          'discount' => 0,
          'fee_types' => []
        ];
      }

      // Process each fee type separately
      foreach ($fee_types as $ft) {
        $transData = $this->db_readonly->select("sum(fsic.component_amount) as totalFee, sum(ifnull(fsic.component_amount_paid,0)) as total_collected_amount, sum(ifnull(fsic.concession_amount,0) + ifnull(fsic.concession_amount_paid,0)) as concession, sum(fsic.component_amount - ifnull(fsic.component_amount_paid,0) - ifnull(fsic.concession_amount,0) - ifnull(fsic.concession_amount_paid,0) ) as balance, fsic.id as fsiD, sum(ifnull(fsic.refund_amount,0)) as refund_amount, fcs.student_id, ifnull(fss.loan_provider_charges,0) as loan_provider_charges, ifnull(fss.discount,'0') as discount_amount, sum(ifnull(fsic.adjustment_amount,0) + ifnull(fsic.adjustment_amount_paid,0)) as adjustment, sum(ifnull(fsi.total_fine_amount,0)) - sum(ifnull(fsi.total_fine_waived,0)) as fine_amount, sum(ifnull(fsi.total_fine_amount,0)) as total_fine, sum(ifnull(fsi.total_fine_waived,0)) as total_fine_waived, sum(ifnull(fsi.total_fine_amount_paid,0)) as total_fine_amount_paid, fsi.id as fsiId, fb.name as fee_type_name, fb.id as fee_type_id")
        ->from('feev2_student_installments_components fsic')
        ->join('feev2_student_installments fsi','fsi.id=fsic.fee_student_installment_id')
        ->join('feev2_student_schedule fss','fsi.fee_student_schedule_id=fss.id')
        ->join('feev2_cohort_student fcs',"fcs.id=fss.feev2_cohort_student_id")
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->where('fcs.blueprint_id', $ft)
        ->where_in('fcs.student_id', $studentIds)
        ->group_by('fcs.student_id')
        ->get()->result();

        // Add class information to transaction data
        foreach ($transData as $key => $trans) {
          if (array_key_exists($trans->student_id, $studentArry)) {
            $trans->class_name = $studentArry[$trans->student_id]->class_name;
            $trans->class_id = $studentArry[$trans->student_id]->clssId;
          }
        }

        // Aggregate data by class
        foreach ($transData as $key => $value) {
          if (array_key_exists($value->class_id, $classArry)) {
            // Add to class totals
            $classArry[$value->class_id]['total_fee'] += $value->totalFee;
            $classArry[$value->class_id]['total_collected_amount'] += $value->total_collected_amount - $value->discount_amount;
            $classArry[$value->class_id]['concession'] += $value->concession;
            $classArry[$value->class_id]['adjustment'] += $value->adjustment;
            $classArry[$value->class_id]['balance'] += $value->balance;
            $classArry[$value->class_id]['loan_provider_charges'] += $value->loan_provider_charges;
            $classArry[$value->class_id]['fine_amount'] += $value->fine_amount;
            $classArry[$value->class_id]['total_fine'] += $value->total_fine;
            $classArry[$value->class_id]['total_fine_waived'] += $value->total_fine_waived;
            $classArry[$value->class_id]['total_fine_amount_paid'] += $value->total_fine_amount_paid;
            $classArry[$value->class_id]['discount'] += $value->discount_amount;
            $classArry[$value->class_id]['refund_amount'] += $value->refund_amount;
            $classArry[$value->class_id]['assigned_count']++;

            // Track fee type specific data
            if (!isset($classArry[$value->class_id]['fee_types'][$value->fee_type_id])) {
              $classArry[$value->class_id]['fee_types'][$value->fee_type_id] = [
                'name' => $value->fee_type_name,
                'total_fee' => 0,
                'total_collected_amount' => 0,
                'concession' => 0,
                'discount' => 0,
                'balance' => 0,
                'assigned_count' => 0,
                'total_fine' => 0,
                'total_fine_waived' => 0,
                'total_fine_amount_paid' => 0,
                'refund_amount' => 0,
                'fine_amount' => 0,
                'loan_provider_charges' => 0,
                'adjustment' => 0,
              ];
            }

            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['total_fee'] += $value->totalFee;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['total_collected_amount'] += $value->total_collected_amount - $value->discount_amount;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['concession'] += $value->concession;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['discount'] += $value->discount_amount;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['balance'] += $value->balance;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['total_fine'] += $value->total_fine;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['total_fine_waived'] += $value->total_fine_waived;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['total_fine_amount_paid'] += $value->total_fine_amount_paid;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['refund_amount'] += $value->refund_amount;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['fine_amount'] += $value->fine_amount;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['loan_provider_charges'] += $value->loan_provider_charges;
            $classArry[$value->class_id]['fee_types'][$value->fee_type_id]['assigned_count']++;
          }
        }
      }

      // Convert to indexed array and sort by class ID
      $final_class_array = [];
      foreach ($class_ids as $cid) {
        if (isset($classArry[$cid])) {
          $final_class_array[] = $classArry[$cid];
        }
      }

      // Calculate grand totals
      $grand_total = [
        'class_name' => 'Grand Total',
        'total_fee' => 0,
        'total_collected_amount' => 0,
        'concession' => 0,
        'adjustment' => 0,
        'balance' => 0,
        'refund_amount' => 0,
        'student_count' => 0,
        'assigned_count' => 0,
        'loan_provider_charges' => 0,
        'fine_amount' => 0,
        'total_fine' => 0,
        'total_fine_waived' => 0,
        'total_fine_amount_paid' => 0,
        'discount' => 0,
        'fee_types' => []
      ];

      foreach ($final_class_array as $class) {
        $grand_total['total_fee'] += $class['total_fee'];
        $grand_total['total_collected_amount'] += $class['total_collected_amount'] - $class['discount'];
        $grand_total['concession'] += $class['concession'];
        $grand_total['adjustment'] += $class['adjustment'];
        $grand_total['balance'] += $class['balance'];
        $grand_total['refund_amount'] += $class['refund_amount'];
        $grand_total['student_count'] += $class['student_count'];
        $grand_total['assigned_count'] += $class['assigned_count'];
        $grand_total['loan_provider_charges'] += $class['loan_provider_charges'];
        $grand_total['fine_amount'] += $class['fine_amount'];
        $grand_total['total_fine'] += $class['total_fine'];
        $grand_total['total_fine_waived'] += $class['total_fine_waived'];
        $grand_total['total_fine_amount_paid'] += $class['total_fine_amount_paid'];
        $grand_total['discount'] += $class['discount'];

        // Aggregate fee type data
        foreach ($class['fee_types'] as $ft_id => $ft_data) {
          if (!isset($grand_total['fee_types'][$ft_id])) {
            $grand_total['fee_types'][$ft_id] = [
              'name' => $ft_data['name'],
              'total_fee' => 0,
              'total_collected_amount' => 0,
              'concession' => 0,
              'discount' => 0,
              'balance' => 0,
              'assigned_count' => 0,
              'total_fine' => 0,
              'total_fine_waived' => 0,
              'total_fine_amount_paid' => 0,
              'refund_amount' => 0,
              'fine_amount' => 0,
              'loan_provider_charges' => 0,
              'adjustment' => 0
            ];
          }

          $grand_total['fee_types'][$ft_id]['total_fee'] += $ft_data['total_fee'];
          $grand_total['fee_types'][$ft_id]['total_collected_amount'] += $ft_data['total_collected_amount'] - $ft_data['discount'];
          $grand_total['fee_types'][$ft_id]['concession'] += $ft_data['concession'];
          $grand_total['fee_types'][$ft_id]['discount'] += $ft_data['discount'];
          $grand_total['fee_types'][$ft_id]['balance'] += $ft_data['balance'];
          $grand_total['fee_types'][$ft_id]['assigned_count'] += $ft_data['assigned_count'];
          $grand_total['fee_types'][$ft_id]['total_fine'] += $ft_data['total_fine'];
          $grand_total['fee_types'][$ft_id]['total_fine_waived'] += $ft_data['total_fine_waived'];
          $grand_total['fee_types'][$ft_id]['total_fine_amount_paid'] += $ft_data['total_fine_amount_paid'];
          $grand_total['fee_types'][$ft_id]['refund_amount'] += $ft_data['refund_amount'];
          $grand_total['fee_types'][$ft_id]['fine_amount'] += $ft_data['fine_amount'];
          $grand_total['fee_types'][$ft_id]['loan_provider_charges'] += $ft_data['loan_provider_charges'];
        }
      }

      return ['classes' => $final_class_array, 'grand_total' => $grand_total];
    }

    public function get_class_wise_dailyTxRepot($blueprint_id, $from_date, $to_date, $classSectionId){
      $this->db_readonly->select("cs.id as clssSectionId, sum(ft.amount_paid) as total_collected_amount, sum(ft.concession_amount) as concession, sum(ifnull(ft.card_charge_amount,0)) as card_charge_amount, sum(ifnull(ft.fine_amount,0)) as fine_amount, ifnull(c.class_name,'') as class_name, fcs.blueprint_id as bpId, c.id as cId")
      ->from('feev2_student_schedule fss')
      ->join('feev2_cohort_student fcs','fcs.id=fss.feev2_cohort_student_id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      // ->where('sy.promotion_status!=','4')
      ->join('class c',"sy.class_id=c.id and c.is_placeholder!=1")
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('feev2_transaction ft',"fss.id=ft.fee_student_schedule_id and ft.status='SUCCESS' and ft.soft_delete!=1")
      ->join('feev2_transaction_payment ftp',"ft.id=ftp.fee_transaction_id and ftp.reconciliation_status!=1")
      ->group_by('c.id')
      ->order_by('c.id, cs.id');
      if ($from_date && $to_date) {
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate =date('Y-m-d',strtotime($to_date));
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($blueprint_id) {
        $this->db_readonly->where_in('fcs.blueprint_id',$blueprint_id);
      }
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $transCount = $this->db_readonly->get()->result();

      $this->db_readonly->select('id as bpId, name as bpName');
      $this->db_readonly->from('feev2_blueprint');
      $this->db_readonly->where('acad_year_id',$this->yearId);
      if ($blueprint_id) {
        $this->db_readonly->where_in('id',$blueprint_id);
      }
      $blueprints = $this->db_readonly->get()->result();


      $classArry = [];
      foreach ($transCount as $key => $value) {
        $classArry[$value->cId][] = $value;
      }
      // return $bleuprints;
      // echo "<pre>"; print_r($bleuprints);die();
      $clsSection = array();
      $blueprints_total = array();
      foreach ($classArry as $clsSectionId => $val) {
        foreach ($val as  $v) {
          if (!array_key_exists($v->bpId,  $blueprints_total)) {
            $blueprints_total[$v->bpId]['bp_amount_paid'] = 0;
            $blueprints_total[$v->bpId]['bp_concession'] = 0;
            $blueprints_total[$v->bpId]['bp_fine_amount'] = 0;
            $blueprints_total[$v->bpId]['bp_card_charge_amount'] = 0;
          }
          $clsSection[$clsSectionId]['blueprint'][$v->bpId] = array();
          foreach ($blueprints as $key => $name) {
            if (!array_key_exists($name->bpId, $clsSection[$clsSectionId]['blueprint'][$v->bpId])) {
              $clsSection[$clsSectionId]['blueprint'][$v->bpId] = array();
            }
          }
          $clsSection[$clsSectionId]['blueprint'][$v->bpId]['amount_paid'] = $v->total_collected_amount;
          $clsSection[$clsSectionId]['blueprint'][$v->bpId]['concession'] = $v->concession;
          $clsSection[$clsSectionId]['blueprint'][$v->bpId]['fine_amount'] = $v->fine_amount;
          $clsSection[$clsSectionId]['blueprint'][$v->bpId]['card_charge_amount'] = $v->card_charge_amount;
          $clsSection[$clsSectionId]['class_section'] = $v->class_name;
          $blueprints_total[$v->bpId]['bp_amount_paid'] += $v->total_collected_amount;
          $blueprints_total[$v->bpId]['bp_concession'] += $v->concession;
          $blueprints_total[$v->bpId]['bp_fine_amount'] += $v->fine_amount;
          $blueprints_total[$v->bpId]['bp_card_charge_amount'] += $v->card_charge_amount;
        }
      }
      return array('headers'=>$blueprints,'blueprints_total'=>$blueprints_total,'result'=>$clsSection);

      // $resDaily = [];
      // foreach ($transCount as  $value) {
      //   foreach ($blueprints as $bp) {
      //     if ($value->bpId == $bp->bpId) {
      //       $resDaily[$bp->bpName][] = $value;
      //     }
      //   }
      // }

      // return $resDaily;


      // $stdCount = $this->db_readonly->select("count(sa.id) as stdCount, cs.id as clssSectionId, concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name")
      // ->from('student_admission sa')
      // ->where('sa.admission_status','2')
      // ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      // ->join('class c',"sy.class_id=c.id and c.is_placeholder!=1")
      // ->join('class_section cs','sy.class_section_id=cs.id')
      // ->group_by('cs.id')
      // ->order_by('c.id, cs.id')
      // ->get()->result();

      // foreach ($transCount as $key => &$val) {
      //   foreach ($stdCount as $key => $std) {
      //     if ($val->clssSectionId == $std->clssSectionId) {
      //       $val->student_count = $std->stdCount;
      //     }
      //   }
      // }
      // return $transCount;
    }

    public function getComponents_feeType($feeType){
      return $this->db_readonly->select('*')->from('feev2_blueprint_components fbc')->get()->result();
    }
    public function getCanceled_receipts_report($from_date, $to_date){
      $this->db_readonly->select("sd.id, sy.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, sd.admission_no, c.id as class_id, ft.receipt_number, ft.amount_paid, ft.id as transId, date_format(paid_datetime,'%d-%m-%Y') as receipt_date, ftp.canceled_remarks, date_format(ftp.recon_submitted_on,'%d-%m-%Y') as deleted_date ");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->group_start();
      $this->db_readonly->or_where('ft.soft_delete', 1);
      $this->db_readonly->or_where('ftp.reconciliation_status', 3);
      $this->db_readonly->group_end();
      // $this->db_readonly->or_where('ft.status','FAILED');
      $this->db_readonly->join('student_admission sd','ft.student_id=sd.id');
      $this->db_readonly->join('student_year sy',"sd.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
      $this->db_readonly->join('class c','sy.class_id=c.id');
      if ($from_date && $to_date) {
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate =date('Y-m-d',strtotime($to_date));
        $this->db_readonly->where('date_format(ftp.recon_submitted_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->order_by('ftp.recon_submitted_on','desc');
      return $this->db_readonly->get()->result();
      // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
    }

    public function generate_canceled_fee_receipt_transaction($transId){
      $trans = $this->db_readonly->select('ft.*, payment_type, bank_name, bank_branch, cheque_or_dd_date, card_reference_number, reconciliation_status, remarks, cheque_dd_nb_cc_dd_number')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('ft.id',$transId)
      ->get()->row();

      $no_of_components = $this->db_readonly->select('count(fbc.name) as comp_count, fb.name as blueprint_name, receipt_for, fb.branches, fb.acad_year_id, fb.id as feev2_blueprint_id')
      ->from('feev2_transaction_installment_component ftic')
      ->where('ftic.fee_transaction_id',$trans->id)
      ->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
      ->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id')
      ->get()->row();

      $no_of_installments = $this->db->select('count(fi.name) as ins_count')
      ->from('feev2_transaction_installment_component ftic')
      ->where('ftic.fee_transaction_id',$trans->id)
      ->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
      ->get()->row();

      $transComp = $this->db_readonly->select('ftic.amount_paid, ftic.concession_amount, fbc.name as compName, fi.name as insName,fi.id as insCompId')
      ->from('feev2_transaction_installment_component ftic')
      ->where('ftic.fee_transaction_id',$trans->id)
      ->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
      ->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id')
      ->order_by('ftic.blueprint_installments_id, ftic.blueprint_component_id')
      ->get()->result();

      $student =  $this->db->select("sd.id as stdId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName,  sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, cs.is_placeholder, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status, cs.section_name as section_name, sy.medium, p.mobile_no, sd.sts_number, sy.is_rte, sd.enrollment_number, sy.roll_no, sy.combination, ifnull(sy.semester,'') as semester")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      ->join("class_section cs", "sy.class_section_id=cs.id",'left')
      ->join("class c", "sy.class_id=c.id",'left')
      ->where('sd.id',$trans->student_id)
      ->where('sy.acad_year_id', $this->yearId) //Todo: Get ID from blueprint
      ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
      ->join("student_relation sr1", "sr1.std_id=sd.id and sr1.relation_type='Mother'")
      ->join("parent p", "p.id=sr.relation_id")
      ->join("parent p1", "p1.id=sr1.relation_id")
      ->get()->row();
      $trans->comp = $transComp;
      $trans->student = $student;
      $trans->no_of_ins = $no_of_installments;
      $trans->no_of_comp = $no_of_components;
      return $trans;
    }

    public function get_consolidate_student_wise_report($clsId, $stdIds, $classSectionId, $blueprintId, $paymentOptions, $acad_year_id, $rte_nrteId){
      $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name, sa.admission_no, fss.total_fee as fee_amount, ifnull(fss.total_fee_paid,0) as paid_amount, fss.payment_status, fss.discount, fss.total_fee - ifnull(fss.total_fee_paid,0) - (ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)) as balance, (ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)) as concession, ifnull(fss.total_fine_amount,0) as total_fine_amount, ifnull(fss.total_card_charge_amount,0) as total_card_charge_amount, fb.id as fbId, fb.name as blueprint_name")
      ->from('feev2_student_schedule fss')
      ->join('feev2_cohort_student fcs',"fcs.id=fss.feev2_cohort_student_id")
      ->join('feev2_blueprint fb',"fcs.blueprint_id=fb.id and fb.acad_year_id=$this->yearId")
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
      // ->where('sy.promotion_status!=','4')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id');
      if ($rte_nrteId) {
        $this->db_readonly->where('sy.is_rte',$rte_nrteId);
      }
      if ($paymentOptions == 1) {
        $this->db_readonly->where('fss.payment_status!=','NOT_STARTED');
      }
      if ($paymentOptions == 2) {
        $this->db_readonly->where('fss.payment_status','NOT_STARTED');
      }
      if ($blueprintId) {
        $this->db_readonly->where_in('fb.id',$blueprintId);
      }
      if ($acad_year_id) {
        $this->db_readonly->where('sa.admission_acad_year_id',$acad_year_id);
      }
      if ($clsId) {
        $this->db_readonly->where_in('c.id',$clsId);
      }
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($stdIds) {
        $this->db_readonly->where_in('sa.id',$stdIds);
      }
      $this->db_readonly->order_by('cs.class_name, cs.section_name, sa.first_name');
      $result = $this->db_readonly->get()->result();


      $this->db_readonly->select('id as bpId, name as bpName');
      $this->db_readonly->from('feev2_blueprint');
      $this->db_readonly->where('acad_year_id',$this->yearId);
      if ($blueprintId) {
        $this->db_readonly->where_in('id',$blueprintId);
      }
      $blueprints = $this->db_readonly->get()->result();

      $stdArray = [];
      foreach ($result as $key => $value) {
        $stdArray[$value->stdId][] = $value;
      }
      // return $bleuprints;
      // echo "<pre>"; print_r($bleuprints);die();
      $stds = array();
      $blueprints_total = array();
      foreach ($stdArray as $stdId => $val) {
        $stds[$stdId]['total_balance']= 0;
        $stds[$stdId]['total_amount']= 0;
        $stds[$stdId]['total_amount_paid']= 0;
        $stds[$stdId]['total_conceission']= 0;
        $stds[$stdId]['total_fine_amount']= 0;
        $stds[$stdId]['total_card_charge_amount']= 0;
        $stds[$stdId]['message'] = '';
        foreach ($val as  $v) {
          if (!array_key_exists($v->blueprint_name,  $blueprints_total)) {
            $blueprints_total[$v->blueprint_name]['bp_total'] = 0;
            $blueprints_total[$v->blueprint_name]['bp_amount_paid'] = 0;
            $blueprints_total[$v->blueprint_name]['bp_concession'] = 0;
            $blueprints_total[$v->blueprint_name]['bp_balance'] = 0;
            $blueprints_total[$v->blueprint_name]['bp_fine_amount'] = 0;
            $blueprints_total[$v->blueprint_name]['bp_card_charge_amount'] = 0;
          }
          $stds[$stdId]['blueprint'][$v->blueprint_name] = array();
          foreach ($blueprints as $key => $name) {
            if (!array_key_exists($name->bpName, $stds[$stdId]['blueprint'][$v->blueprint_name])) {
              $stds[$stdId]['blueprint'][$v->blueprint_name] = array();
            }
          }
          $stds[$stdId]['blueprint'][$v->blueprint_name]['total_amount'] = $v->fee_amount;
          $stds[$stdId]['blueprint'][$v->blueprint_name]['amount_paid'] = $v->paid_amount;
          $stds[$stdId]['blueprint'][$v->blueprint_name]['concession'] = $v->concession;
          $stds[$stdId]['blueprint'][$v->blueprint_name]['balance'] = $v->balance;
          $stds[$stdId]['blueprint'][$v->blueprint_name]['fine_amount'] = $v->total_fine_amount;
          $stds[$stdId]['blueprint'][$v->blueprint_name]['card_charge'] = $v->total_card_charge_amount;
          $stds[$stdId]['student_name'] = $v->student_name;
          $stds[$stdId]['class_section'] = $v->class_name;
          $stds[$stdId]['admission_no'] = $v->admission_no;
          $stds[$stdId]['total_balance'] += $v->balance;
          $stds[$stdId]['total_amount'] += $v->fee_amount;
          $stds[$stdId]['total_amount_paid'] += $v->paid_amount;
          $stds[$stdId]['total_conceission'] += $v->concession;
          $stds[$stdId]['total_fine_amount'] += $v->total_fine_amount;
          $stds[$stdId]['total_card_charge_amount'] += $v->total_card_charge_amount;
          $stds[$stdId]['message'] .= $v->blueprint_name.': '.($v->balance).' ';
          $blueprints_total[$v->blueprint_name]['bp_total'] += $v->fee_amount;
          $blueprints_total[$v->blueprint_name]['bp_amount_paid'] += $v->paid_amount;
          $blueprints_total[$v->blueprint_name]['bp_concession'] += $v->concession;
          $blueprints_total[$v->blueprint_name]['bp_balance'] += $v->balance;
          $blueprints_total[$v->blueprint_name]['bp_fine_amount'] += $v->total_fine_amount;
          $blueprints_total[$v->blueprint_name]['bp_card_charge_amount'] += $v->total_card_charge_amount;
        }
      }
      return array('headers'=>$blueprints,'blueprints_total'=>$blueprints_total,'result'=>$stds);
    }

    public function get_blueprints_all(){
      return $this->db_readonly->where('acad_year_id',$this->yearId)->get('feev2_blueprint')->result();
    }

    public function get_sms_template_for_balance(){
      return $this->db_readonly->select('id, name')
      ->from('sms_template_new')
      ->where('category','Fees')
      ->get()->result();
    }

    public function get_sms_content_for_balance($tempId){
      return $this->db_readonly->select('content')
      ->from('sms_templates_acad_year')
      ->where('sms_templates_id',$tempId)
      ->where('acad_year_id',$this->yearId)
      ->get()->row();
    }

    public function get_sms_content_for_balancebyid($tempId){

      return $this->db_readonly->select('content')
      ->from('sms_templates_acad_year')
      ->where('sms_templates_id',$tempId)
      ->get()->row();
    }

    public function get_daily_fee_transcation_prarthana_count($fee_type,$from_date,$to_date){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("ft.student_id");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id');
      $this->db_readonly->where_in('fbit.feev2_blueprint_id',$fee_type);
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->where('ft.soft_delete !=',1);
      $this->db_readonly->where('ftp.reconciliation_status !=',3);
      $this->db_readonly->where('ft.status','SUCCESS');
      $this->db_readonly->group_by('ft.student_id');
      $this->db_readonly->order_by('paid_datetime','desc');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $trans = $this->db_readonly->get()->result();
      $studentIDs = [];
      foreach ($trans as $key => $val) {
        array_push($studentIDs, $val->student_id);
      }
      return $studentIDs;

    }
    public function get_daily_fee_transcation_prarthana($fee_type,$from_date,$to_date)
    {
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("format(ft.amount_paid,2,'EN_IN') as paid_amount, c.class_name, cs.section_name, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name,  CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as father_name, ftp.reconciliation_status, ft.id as transId, ifnull(ftp.bank_name,'') as  bank_name, date_format(ftp.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, ifnull(ftp.cheque_dd_nb_cc_dd_number,'') as cheque_dd_nb_cc_dd_number, sy.picture_url, sd.gender, ftp.payment_type");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id');
      if ($fee_type == '1') {
        $this->db_readonly->where_in('fbit.feev2_blueprint_id',array('1','4','7'));
      }else if($fee_type == '2'){
        $this->db_readonly->where_in('fbit.feev2_blueprint_id',array('2','5','8'));
      }else if($fee_type == '3'){
        $this->db_readonly->where_in('fbit.feev2_blueprint_id',array('3','6','9'));
      }

      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->join('student_admission sd','ft.student_id=sd.id');
      // $this->db_readonly->where('sd.admission_status','2');
      $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id');
      // $this->db_readonly->where('sy.promotion_status!=','4');
      $this->db_readonly->join('student_relation sr',"sd.id=sr.std_id and relation_type = 'Father'");
      $this->db_readonly->join('parent p','sr.relation_id=p.id');
      $this->db_readonly->where('sy.acad_year_id',$this->yearId);
      $this->db_readonly->join('class c','sy.class_id=c.id');
      $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id');
      $this->db_readonly->where('ft.soft_delete !=',1);
      $this->db_readonly->where('ftp.reconciliation_status !=',3);
      $this->db_readonly->where('ft.status','SUCCESS');
      $this->db_readonly->order_by('paid_datetime','desc');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $result = $this->db_readonly->get()->result();

      foreach ($result as  &$res) {
        $res->paymentValue = $this->_get_PaymentValue($res->payment_type);
      }
      return $result;
    }

    public function get_daily_fee_transcation_prarthana_new($fee_type,$from_date,$to_date, $student_ids){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("format(ft.amount_paid,2,'EN_IN') as paid_amount,  ftp.reconciliation_status, ft.id as transId, ifnull(ftp.bank_name,'') as  bank_name, date_format(ftp.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, ifnull(ftp.cheque_dd_nb_cc_dd_number,'') as cheque_dd_nb_cc_dd_number, ftp.payment_type, ft.student_id");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->where_in('ft.student_id',$student_ids);
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_blueprint_installment_types fbit','fss.feev2_blueprint_installment_types_id=fbit.id');
      $this->db_readonly->where_in('fbit.feev2_blueprint_id',$fee_type);
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->where('ft.soft_delete !=',1);
      $this->db_readonly->where('ftp.reconciliation_status !=',3);
      $this->db_readonly->where('ft.status','SUCCESS');
      $this->db_readonly->order_by('paid_datetime','desc');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $trans = $this->db_readonly->get()->result();

      $studentIds = [];
      foreach ($trans as $key => $tr) {
        array_push($studentIds, $tr->student_id);
      }
      if (!empty($studentIds)) {
        $studentDetails = $this->db_readonly->select("c.class_name, c.id as cId, cs.section_name, cs.id as csId, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, ifnull(sd.first_name,'') as first_name,  CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as father_name, sy.picture_url, sd.gender, sd.id as stdId")
        ->from('student_admission sd')
        ->where_in('sd.id',$studentIds)
        ->join('feev2_cohort_student fcs','sd.id=fcs.student_id')
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->where_in('fb.id',$fee_type)
        // ->where_in('sd.id',array('1','2','3'))
        ->join('student_year sy','sd.id=sy.student_admission_id')
        ->join('student_relation sr',"sd.id=sr.std_id and relation_type = 'Father'")
        ->join('parent p','sr.relation_id=p.id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->order_by('c.id,cs.id,sd.first_name')
        ->get()->result();
      }
      foreach ($trans as $key => &$tr) {
        if (!empty($studentDetails)) {
          foreach ($studentDetails as $key => $val) {
            if ($tr->student_id == $val->stdId) {
              $tr->class_name = $val->class_name;
              $tr->cId = $val->cId;
              $tr->section_name = $val->section_name;
              $tr->csId = $val->csId;
              $tr->student_name = $val->student_name;
              $tr->father_name = $val->father_name;
              $tr->picture_url = $val->picture_url;
              $tr->gender = $val->gender;
            }
          }
        }
        $tr->paymentValue = $this->_get_PaymentValue($tr->payment_type);
      }
      // array_multisort(array_column($trans, 'cId'), SORT_ASC, array_column($trans, 'csId'), SORT_ASC, array_column($trans, 'first_name'), SORT_ASC, $trans);
      return $trans;
    }
    public function get_day_books_details_headers($fee_type_id){

      $headers_fees = $this->db_readonly->select('fbc.id as bpCompId, fbc.name')
      ->from('feev2_blueprint_components fbc')
      ->where_in('fbc.feev2_blueprint_id',$fee_type_id)
      ->get()->result();
      $headers_sales = array();
      if (in_array("sales",$fee_type_id)) {
       $headers_sales = $this->db_readonly->select('ipv.id as variantId, ipv.name')
        ->from('inventory_product_variant ipv')
        ->get()->result();
      }
      $headers = array_merge($headers_fees,$headers_sales);
      return $headers;
    }




    public function get_day_books_details_fees_tx($fee_type_id,$from_date,$to_date){

      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("ft.id as transId, sum(ftic.amount_paid) as amount_paid, ftic.blueprint_component_id as compId");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->where('ft.soft_delete !=',1);
      $this->db_readonly->where('ft.status','SUCCESS');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id');
      $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id');
      $this->db_readonly->where_in('fbc.feev2_blueprint_id',$fee_type_id);
      $this->db_readonly->group_by('ft.id');
      $this->db_readonly->group_by('fbc.id');
      $transComp = $this->db_readonly->get()->result();

      $this->db_readonly->select("ft.id as transId, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, receipt_number, fb.receipt_book_id, CONCAT(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, ftp.reconciliation_status, ftp.bank_name, ftp.cheque_or_dd_date, ftp.cheque_dd_nb_cc_dd_number, ftp.recon_submitted_on, ftp.payment_type");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->where('ft.soft_delete !=',1);
      $this->db_readonly->where('ft.status','SUCCESS');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_cohort_student fcs','fcs.id=fss.feev2_cohort_student_id');
      $this->db_readonly->where_in('fcs.blueprint_id',$fee_type_id);
      $this->db_readonly->join('feev2_blueprint fb','fb.id=fcs.blueprint_id');
      $this->db_readonly->join('student_admission sd','ft.student_id=sd.id');
      // $this->db_readonly->where('sd.admission_status','2');
      // $this->db_readonly->where('sy.promotion_status!=','4');
      $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id');
      $this->db_readonly->where('sy.acad_year_id',$this->yearId);
      $this->db_readonly->join('class c','sy.class_id=c.id');
      $this->db_readonly->join('class_section cs','cs.id=sy.class_section_id','left');
      // $this->db_readonly->order_by('ft.paid_datetime','desc');
      $this->db_readonly->order_by('ft.receipt_number','asc');
      $transStd = $this->db_readonly->get()->result();


      foreach ($transStd as $key => &$val) {
        if (!empty($transComp)) {
          foreach ($transComp as $key => $comp) {
            if ($val->transId == $comp->transId) {
              $val->Components[$comp->compId] = $comp->amount_paid;
            }
          }
        }

      }
      // echo "<pre>"; print_r($transStd); die();
      return $transStd;
    }

    public function get_day_books_details_sale_tx($from_date,$to_date,$paymentModes, $include_delete){

      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("sm.id as transId, sum(st.amount) as amount_paid, concat('-1',st.inventory_product_variant_id) as compId");
      $this->db_readonly->from('sales_master sm');
      if ($include_delete == 0) {
        $this->db_readonly->where('sm.soft_delete!=1');
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->join('sales_transactions st','sm.id=st.sales_master_id');
      $this->db_readonly->group_by('st.sales_master_id');
      // $this->db_readonly->group_by('st.inventory_product_id');
      $this->db_readonly->group_by('st.inventory_product_variant_id');
      $this->db_readonly->group_by('sm.id');
      $transComp = $this->db_readonly->get()->result();

      $this->db_readonly->select("sm.id as transId, (case when(c.class_name IS NULL) then ifnull(sm.class_name, 'NA') else c.class_name  end) as class_name, '' as sectionName, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date, receipt_no as receipt_number, ipc.receipt_book_id, sales_type,  parent_name, (case when sm.student_id = 0 then sm.student_name else CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) end) as student_name, sm.payment_type, bank_name, bank_branch,cheque_dd_number as cheque_dd_nb_cc_dd_number, recon_submitted_on,cheque_dd_date as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '0' as adjustment_amount, '-3' as bpId, '0' as reconciliation_status, sm.remarks, (case when sm.soft_delete = 1 then 0 else sm.total_amount end) as amountPaid, sm.soft_delete, '0' as loan_provider_charges, ifnull(sem.sem_name,'NA') as semester, '0' as refund_amount, sm.created_by, ifnull(sd.admission_no,'NA') as admission_no, ifnull(sd.enrollment_number,'NA') as enrollment_number");
      $this->db_readonly->from('sales_master sm');
      if ($include_delete == 0) {
        $this->db_readonly->where('sm.soft_delete!=1');
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($paymentModes) {
        $this->db_readonly->where_in('sm.payment_type',$paymentModes);
      }
      $this->db_readonly->join('inventory_product_category ipc','sm.category_id=ipc.id');
      $this->db_readonly->join('student_admission sd',"sm.student_id=sd.id",'left');
      $this->db_readonly->join('student_year sy',"sd.id=sy.student_admission_id and sy.acad_year_id=sm.acad_year_id",'left');
      // $this->db_readonly->where('sy.promotion_status!=','4');
      $this->db_readonly->join('class c','sy.class_id=c.id','left');
      $this->db_readonly->join('semester sem','sy.semester=sem.id','left');
      // $this->db_readonly->order_by('sm.receipt_date','desc');
      $this->db_readonly->group_by('sm.id');
      $this->db_readonly->order_by('sm.receipt_no','asc');
      $transStd = $this->db_readonly->get()->result();
      foreach ($transStd as $key => &$val) {
        foreach ($transComp as $key => $comp) {
          if ($val->transId == $comp->transId) {
            $val->Components[$comp->compId] = ($val->soft_delete == 1) ? 0 : $comp->amount_paid;
            $val->amount_paid = ($val->soft_delete == 1) ? 0 : $val->amountPaid;
            $val->fee_type = 'Sales';
            $val->collected_name = $this->_getAvatarNameById($val->created_by);
          }
        }
      }
      return $transStd;
    }

    public function get_day_books_details_appliation_tx($from_date, $to_date, $paymentModes){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      // $this->db_readonly->select("af.id as transId,  (case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amount_paid, '-1' as compId");
      // $this->db_readonly->from('admission_forms af');
      // $this->db_readonly->join('admission_status as','af.id=as.af_id');
      // $this->db_readonly->where('as.payment_status','SUCCESS');
      // // $this->db_readonly->where('af.academic_year_applied_for',$this->yearId);
      // $this->db_readonly->where('af.application_no !=','');
      // if ($fromDate && $toDate) {
      //   $this->db_readonly->where('date_format(af.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      // }
      // $this->db_readonly->join('admission_transaction at',"af.id=at.af_id  and af.filled_by !=0 ",'left');
      // $this->db_readonly->join('online_application_fee_payment_master opf',"af.id=opf.source_id and opf.tx_response_code=0",'left');
      // $transComp  = $this->db_readonly->get()->result();

      $query="SELECT `af`.`id` as `transId`, (case when af.receipt_number IS NULL then  af.application_no else af.receipt_number end) as receipt_number, ifnull(af.std_name, '') as student_name, ifnull(af.grade_applied_for, '') as class_name, '' as sectionName, (case when af.filled_by = 0 then 10 when af.filled_by !=0 then at.payment_type end) as payment_type, `at`.`bank_name`, `at`.`branch_name` as `bank_branch`, `at`.`cheque_or_dd_number` as `cheque_dd_nb_cc_dd_number`, date_format( at.cheque_or_dd_date, '%d-%m-%Y') as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '0' as adjustment_amount, '-2' as bpId, '0' as reconciliation_status, (case when af.filled_by = 0 then '' when af.filled_by !=0 then at.remarks end) as remarks, (case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amountPaid, '0' as soft_delete, '0' as loan_provider_charges, 'NA' as semester, '0' as refund_amount, date_format(af.created_on,'%d-%m-%Y') as receipt_date, (case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amount_paid, '-1' as compId, 'Application' as fee_type, at.collected_by, '-' as admission_no, 'NA' as enrollment_number
      FROM `admission_forms` `af`
      JOIN `admission_status` `as` ON `af`.`id`=`as`.`af_id`
      LEFT JOIN `admission_transaction` `at` ON `af`.`id`=`at`.`af_id` and `af`.`filled_by` !=0
      LEFT JOIN `online_application_fee_payment_master` `opf` ON `af`.`id`=`opf`.`source_id` and `opf`.`tx_response_code`=0
      WHERE `as`.`payment_status` = 'SUCCESS'
      AND `af`.`application_no` != ''
      and  date_format(af.created_on,'%Y-%m-%d') BETWEEN '".$fromDate."' and '".$toDate."'
      ORDER BY `af`.`application_no` ASC";

      $transStd = $this->db_readonly->query($query)->result();

      // $this->db_readonly->select("af.id as transId, (case when af.receipt_number IS NULL then  af.application_no else af.receipt_number end) as receipt_number,  ifnull(af.std_name,'') as student_name, ifnull(af.grade_applied_for,'') as class_name, '' as sectionName, (case when af.filled_by = 0 then 10 when af.filled_by !=0 then at.payment_type end) as payment_type, at.bank_name, at.branch_name as bank_branch, at.cheque_or_dd_number as cheque_dd_nb_cc_dd_number, date_format( at.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '0' as adjustment_amount, '-2' as bpId, '0' as reconciliation_status, (case when af.filled_by = 0 then '' when af.filled_by !=0 then at.remarks end) as remarks,(case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amountPaid, '0' as soft_delete, '0' as loan_provider_charges, 'NA' as semester, '0' as refund_amount, date_format(af.created_on,'%d-%m-%Y') as receipt_date");
      // $this->db_readonly->from('admission_forms af');
      // $this->db_readonly->join('admission_status as','af.id=as.af_id');
      // $this->db_readonly->where('as.payment_status','SUCCESS');
      // // $this->db_readonly->where('af.academic_year_applied_for',$this->yearId);
      // $this->db_readonly->where('af.application_no !=','');
      // if ($fromDate && $toDate) {
      //   $this->db_readonly->where('date_format(af.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      // }
      // $this->db_readonly->join('admission_transaction at',"af.id=at.af_id  and af.filled_by !=0 ",'left');
      // $this->db_readonly->join('online_application_fee_payment_master opf',"af.id=opf.source_id and opf.tx_response_code=0",'left');
      // $this->db_readonly->order_by('af.application_no','asc');
      // $transStd = $this->db_readonly->get()->result();

      if ($paymentModes) {
        $transStds = [];
        foreach ($transStd as $key => $val) {
          if (in_array($val->payment_type, $paymentModes)) {
            array_push($transStds, $val);
          }
        }
      }else{
        $transStds = $transStd;
      }

      foreach ($transStds as $key => $val) {
        $val->Components[$val->compId] = $val->amount_paid;
        $val->total_fee = $val->amount_paid;
        $val->fee_type = 'Application';
        if(!empty($val->collected_by)){
          $val->collected_name = $this->_getAvatarNameById($val->collected_by);
        }else{
          $val->collected_name = '-';
        }
      }
      return $transStds;
    }

    public function get_day_books_details_headers_accounts($fee_type_id){
      $headers_fees = $this->db_readonly->select('a.id as accountId, a.account')
      ->from('feev2_blueprint_components fbc')
      ->join('accounts a','fbc.vendor_code=a.tracknpay_vendor_id')
      ->where_in('fbc.feev2_blueprint_id',$fee_type_id)
      ->group_by('fbc.vendor_code')
      ->get()->result();
      return $headers_fees;

      // echo "<pre>"; print_r($headers_fees); die();
    }

    public function get_day_books_details_fees_tx_account($fee_type_id,$from_date){

      $fromDate = date('Y-m-d',strtotime($from_date));

      $this->db_readonly->select("ft.id as transId, ftic.amount_paid, ftic.blueprint_component_id as compId");
      $this->db_readonly->from('feev2_transaction ft');
      if ($fromDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d")', $fromDate);
      }
      $this->db_readonly->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id');
      $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id');
      $this->db_readonly->join('accounts a','fbc.vendor_code=a.tracknpay_vendor_id');
      $this->db_readonly->where_in('fbc.feev2_blueprint_id',$fee_type_id);
      $this->db_readonly->group_by('fbc.vendor_code');
      $transComp = $this->db_readonly->get()->result();

      $this->db_readonly->select("ft.id as transId, c.class_name, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, receipt_number, fb.receipt_book_id");
      $this->db_readonly->from('feev2_transaction ft');
      if ($fromDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d")"', $fromDate);
      }
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_cohort_student fcs','fcs.id=fss.feev2_cohort_student_id');
      $this->db_readonly->where_in('fcs.blueprint_id',$fee_type_id);
      $this->db_readonly->join('feev2_blueprint fb','fb.id=fcs.blueprint_id');
      $this->db_readonly->join('student_admission sd','ft.student_id=sd.id');
      // $this->db_readonly->where('sd.admission_status','2');
      // $this->db_readonly->where('sy.promotion_status!=','4');
      $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id');
      $this->db_readonly->join('class c','sy.class_id=c.id');
      $transStd = $this->db_readonly->get()->result();


      foreach ($transStd as $key => &$val) {
        foreach ($transComp as $key => $comp) {
          if ($val->transId == $comp->transId) {
            $val->Components[$comp->compId] = $comp->amount_paid;
          }
        }
      }

      return $transStd;
    }

  public function get_blueprints_accounts_sales(){
    $salsComp= $this->db_readonly->select('ac.account, ipv.id as ipvId, ac.tracknpay_vendor_id')
    ->from('accounts ac')
    ->join('inventory_product_variant ipv','ac.tracknpay_vendor_id =ipv.vendor_code')
    ->group_by('ipv.vendor_code')
    ->get()->result();

    $salsBp =  $this->db_readonly->select('ipv.id as ipvId, a.name')
    ->from('inventory_product_variant ipv')
    ->join('accounts a','a.tracknpay_vendor_id=ipv.vendor_code')
    ->get()->result();

    foreach ($salsBp as $key => &$bp) {
      foreach ($salsComp as $key => $bpComp) {
        if ($bp->ipvId == $bpComp->ipvId) {
          // echo "<pre>"; print_r($bpComp);
          $bp->accounts[$bpComp->tracknpay_vendor_id] = $bpComp;
        }
      }
    }
    return $salsBp;
  }
  public function get_blueprints_accounts(){

    // $sales_account_number = $this->settings->getSetting('fees_sales_account_number');
    // $sAccount ='NA';
    // if (!empty($sales_account_number)) {
    //   $sAccount = $sales_account_number;
    // }

    $bpComps= $this->db_readonly->select('ac.account, fbc.feev2_blueprint_id, ac.tracknpay_vendor_id')
    ->from('accounts ac')
    ->join('feev2_blueprint_components fbc','ac.tracknpay_vendor_id =fbc.vendor_code')
    ->join('feev2_blueprint fb','fb.id=fbc.feev2_blueprint_id')
    ->where('fb.acad_year_id',$this->yearId)
    ->group_by('fbc.vendor_code')
    ->get()->result();

    $salsComp= $this->db_readonly->select('ac.account, ipv.id as feev2_blueprint_id, ac.tracknpay_vendor_id')
    ->from('accounts ac')
    ->join('inventory_product_variant ipv','ac.tracknpay_vendor_id =ipv.vendor_code')
    ->group_by('ipv.vendor_code')
    ->get()->result();

    $bpCompMerge = array_merge($bpComps,$salsComp);

    // echo "<pre>"; print_r($bpComps); die();
    // $salsComp= $this->db_readonly->select('ac.account, ipv.id as ipvId, ac.tracknpay_vendor_id')
    // ->from('accounts ac')
    // ->join('inventory_product_variant ipv','ac.tracknpay_vendor_id =ipv.vendor_code')
    // ->group_by('ipv.vendor_code')
    // ->get()->result();

    // $salsComp[] = (object) array('account'=>$sAccount,'feev2_blueprint_id'=>'-1','tracknpay_vendor_id'=>'sales');
    // $bpCompMerge = array_merge($bpComps,$salsComp);

    $blueprints =  $this->db_readonly->select('id,name,description')->from('feev2_blueprint')->where('acad_year_id',$this->yearId)->get()->result();

    $salsBp =  $this->db_readonly->select('ipv.id as id, a.name')
    ->from('inventory_product_variant ipv')
    ->join('accounts a','a.tracknpay_vendor_id=ipv.vendor_code')
    ->get()->result();

    // $salsBp =  $this->db_readonly->select('id as ipvId, name')->from('inventory_product_variant')->get()->result();
    // $salsBp[] = (object) array('id'=>'-1','name'=>'Sales','description'=>'Sales');

    $bpMerge = array_merge($blueprints,$salsBp);



    foreach ($bpMerge as $key => &$bp) {
      foreach ($bpCompMerge as $key => $bpComp) {
        if ($bp->id == $bpComp->feev2_blueprint_id) {
          // echo "<pre>"; print_r($bpComp);
          $bp->accounts[$bpComp->tracknpay_vendor_id] = $bpComp;
        }
      }
    }


    // foreach ($bpCompMerge as $key => &$bp) {
    //   foreach ($bpComps as $key => $bpComp) {
    //     if ($bp->id == $bpComp->feev2_blueprint_id) {
    //       $bp->accounts[$bpComp->tracknpay_vendor_id] = $bpComp;
    //     }
    //   }
    // }

    // foreach ($bpMerge as $key => &$bp) {
    //   foreach ($salsComp as $key => $bpComp) {
    //     if ($bp->ipvId == $bpComp->ipvId) {
    //       $bp->accounts[$bpComp->tracknpay_vendor_id] = $bpComp;
    //     }
    //   }
    // }

    // $bpMerge = array_merge($blueprints,$salsBp);

    return $blueprints;
  }

  public function get_transactions_bpWise($from_date){
    $fromDate = date('Y-m-d',strtotime($from_date));

    $beforeDate = date('Y-m-d', strtotime($from_date. " -1 days"));

    $this->db_readonly->select("ft.id as transId, sum(ftic.amount_paid) as amount_paid, fbc.vendor_code, ft.student_id, sum(ftic.concession_amount) as concession");
    $this->db_readonly->from('feev2_transaction ft');
    $this->db_readonly->where('ft.soft_delete !=',1);
    $this->db_readonly->where('ft.status','SUCCESS');
    $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d")',$fromDate);
    $this->db_readonly->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id');
    $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id = fbc.id');
    $this->db_readonly->group_by('fbc.vendor_code');
    $this->db_readonly->group_by('ft.student_id');
    $transComp = $this->db_readonly->get()->result();

    $studentIds = [];
    foreach ($transComp as $key => $val) {
      array_push($studentIds, $val->student_id);
    }
    // $this->db_readonly->select("sm.id as salesId, sum(st.amount) as amount_paid, sm.student_id, (case when ipv.vendor_code IS NOT NULL then ipv.vendor_code else 'sales' end) as vendor_code, '0' as concession");
    // $this->db_readonly->from('sales_master sm');
    // $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d")',$fromDate);
    // $this->db_readonly->join('sales_transactions st','st.sales_master_id=sm.id');
    // $this->db_readonly->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id');
    // $this->db_readonly->group_by('ipv.vendor_code');
    // $this->db_readonly->group_by('sm.student_id');
    // $salesComp = $this->db_readonly->get()->result();

    // $accComp = array_merge($transComp, $salesComp);
    if (empty($studentIds)) {
      return array();
    }
    $this->db_readonly->select("fcs.student_id as student_id,  c.class_name, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, sum(fss.total_fee) as fees_payable");
    $this->db_readonly->from('feev2_cohort_student fcs');
    $this->db_readonly->where_in('fcs.student_id',$studentIds);
    $this->db_readonly->join('feev2_student_schedule fss','fcs.id = fss.feev2_cohort_student_id');
    // $this->db_readonly->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id');
    // $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d")',$fromDate);
    // $this->db_readonly->where('ft.soft_delete !=',1);
    // $this->db_readonly->where('ft.status','SUCCESS');
    $this->db_readonly->join('student_admission sd',"fcs.student_id=sd.id");
    $this->db_readonly->join('student_year sy',"sd.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
    // $this->db_readonly->where('sy.promotion_status!=','4');
    // $this->db_readonly->join('class c','sy.class_id=c.id');
    $this->db_readonly->join('class c',"sy.class_id=c.id and c.acad_year_id = $this->yearId and is_placeholder !=1");
    $this->db_readonly->group_by('fcs.student_id');
    $transStd = $this->db_readonly->get()->result();
    // $this->db_readonly->select("sm.student_id as student_id, sm.total_amount as txFeePaid, (case when c.class_name IS NOT NULL then c.class_name else 'NA' end) as class_name, (case when sm.student_name IS NOT NULL then sm.student_name else CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) end) as student_name,  DATE_SUB(sm.receipt_date, INTERVAL 1 DAY)  as date, '0' as fees_payable");
    // $this->db_readonly->from('sales_master sm');
    // $this->db_readonly->join('student_admission sd',"sm.student_id=sd.id",'left');
    // $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id','left');
    // // $this->db_readonly->where('sy.promotion_status!=','4');
    // $this->db_readonly->join('class c','sy.class_id=c.id','left');
    // $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d")',$fromDate);
    // $this->db_readonly->group_by('sm.student_id');
    // $salesStd = $this->db_readonly->get()->result();

    // $accStd = array_merge($transStd, $salesStd);


    $this->db_readonly->select("sum(ft.amount_paid) as previous_date_amount_paid, ft.student_id");
    $this->db_readonly->from('feev2_transaction ft');
    $this->db_readonly->where('ft.soft_delete !=',1);
    $this->db_readonly->where('ft.status','SUCCESS');
    $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d")<',$fromDate);
    $this->db_readonly->where('acad_year_id',$this->yearId);
    $this->db_readonly->group_by('ft.student_id');
    $transBefore = $this->db_readonly->get()->result();
    foreach ($transStd as $key => &$val) {
      $val->previousDateFeePaid = 0;
      if (!empty($transBefore)) {
        foreach ($transBefore as $key => $trans) {
          if (!empty($trans->student_id)) {
            if ($val->student_id == $trans->student_id) {
              $val->previousDateFeePaid = $trans->previous_date_amount_paid;
            }
          }
        }
      }
      foreach ($transComp as $key => $comp) {
        if ($val->student_id == $comp->student_id) {
          $val->components[$comp->vendor_code] = $comp->amount_paid;
          $val->concession[$comp->vendor_code] = $comp->concession;
        }
      }
    }

   return $transStd;
  }

  public function get_transactions_bpWise_sales($from_date){
    $fromDate = date('Y-m-d',strtotime($from_date));

    $beforeDate = date('Y-m-d', strtotime($from_date. " -1 days"));
    $this->db_readonly->select("sm.id as salesId, sum(st.amount) as amount_paid, sm.student_id, (case when ipv.vendor_code IS NOT NULL then ipv.vendor_code else 'sales' end) as vendor_code, '0' as concession");
    $this->db_readonly->from('sales_master sm');
    $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d")',$fromDate);
    $this->db_readonly->where('sm.soft_delete!=1');
    $this->db_readonly->join('sales_transactions st','st.sales_master_id=sm.id');
    $this->db_readonly->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id');
    $this->db_readonly->group_by('ipv.vendor_code');
    $this->db_readonly->group_by('sm.id');
    $salesComp = $this->db_readonly->get()->result();

    $this->db_readonly->select("sm.student_id as student_id, sm.total_amount as txFeePaid, (case when c.class_name IS NOT NULL then c.class_name else 'NA' end) as class_name, (case when sm.student_name IS NOT NULL then sm.student_name else CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) end) as student_name,  DATE_SUB(sm.receipt_date, INTERVAL 1 DAY)  as date, sm.total_amount as fees_payable, sm.id as salesId");
    $this->db_readonly->from('sales_master sm');
    $this->db_readonly->join('student_admission sd',"sm.student_id=sd.id",'left');
    $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id','left');
    $this->db_readonly->join('class c','sy.class_id=c.id','left');
    $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d")',$fromDate);
    $this->db_readonly->where('sm.soft_delete!=1');
    $this->db_readonly->group_by('sm.id');
    $salesStd = $this->db_readonly->get()->result();


    foreach ($salesStd as $key => &$val) {
      foreach ($salesComp as $key => $comp) {
        if ($val->salesId == $comp->salesId) {
          $val->components[$comp->vendor_code] = $comp->amount_paid;
          $val->concession[$comp->vendor_code] = $comp->concession;
        }
      }
      $val->previousDateFeePaid = 0;
    }

   return $salesStd;

  }

  public function get_openening_balance($from_date){
    $fromDate = date('Y-m-d',strtotime($from_date));

    $beforeDate = date('Y-m-d', strtotime($from_date. " -1 days"));

    $this->db_readonly->select("sum(ftic.amount_paid) as amount_paid, fbc.vendor_code, ft.student_id, sum(ftic.concession_amount) as concession");
    $this->db_readonly->from('feev2_transaction ft');
    $this->db_readonly->where('ft.soft_delete !=',1);
    $this->db_readonly->where('ft.status','SUCCESS');
    $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d")<',$fromDate);
    $this->db_readonly->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id');
    $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id = fbc.id');
    $this->db_readonly->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id');
    $this->db_readonly->where('fb.acad_year_id',$this->yearId);
    $this->db_readonly->group_by('fbc.vendor_code');
    $transComp = $this->db_readonly->get()->result();


    // $this->db_readonly->select("sum(st.amount) as amount_paid, sm.student_id,(case when ipv.vendor_code IS NOT NULL then ipv.vendor_code else 'sales' end) as vendor_code, '0' as concession");
    // $this->db_readonly->from('sales_master sm');
    // $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d")<',$fromDate);
    // $this->db_readonly->join('sales_transactions st','st.sales_master_id=sm.id');
    // $this->db_readonly->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id');

    // $salesComp = $this->db_readonly->get()->result();

    // $mergeComp = array_merge($transComp, $salesComp);
    $ob_accounts =[];
    foreach ($transComp as $key => $comp) {
      $ob_accounts['t_amount'][$comp->vendor_code] = $comp->amount_paid;
      $ob_accounts['t_con'][$comp->vendor_code] = $comp->concession;
    }

    // $this->db_readonly->select('ifnull(sum(ft.amount_paid),0)  as trsAmount');
    // $this->db_readonly->from('feev2_transaction ft');
    //  $this->db_readonly->where('ft.soft_delete !=',1);
    // $this->db_readonly->where('ft.status','SUCCESS');
    // $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d")',$beforeDate);
    // $transBefore = $this->db_readonly->get()->row();
    // $b = (!empty($transBefore)) ? $transBefore->trsAmount : 0;

    $this->db_readonly->select("ifnull(fss.total_fee,0) as ob_fees_payable");
    $this->db_readonly->from('feev2_student_schedule fss');
    $this->db_readonly->where("fss.id in (select ft.fee_student_schedule_id from feev2_transaction ft where date_format(ft.paid_datetime,'%Y-%m-%d') <'$fromDate')");
    $obBalnce = $this->db_readonly->get()->row();
    $a = (!empty($obBalnce)) ? $obBalnce->ob_fees_payable : 0;

    // $ob =  $a - $b;
    $ob =  $a;
    return array('ob'=>$ob,'account_ob'=>$ob_accounts);

  }

   public function get_openening_balance_sales($from_date){
    $fromDate = date('Y-m-d',strtotime($from_date));

    $beforeDate = date('Y-m-d', strtotime($from_date. " -1 days"));

    $this->db_readonly->select("sum(st.amount) as amount_paid, sm.student_id,(case when ipv.vendor_code IS NOT NULL then ipv.vendor_code else 'sales' end) as vendor_code, '0' as concession");
    $this->db_readonly->from('sales_master sm');
    $this->db_readonly->where('sm.acad_year_id',$this->yearId);
    $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d")<',$fromDate);
    $this->db_readonly->join('sales_transactions st','st.sales_master_id=sm.id');
    $this->db_readonly->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id');

    $salesComp = $this->db_readonly->get()->result();


    $ob_accounts =[];
    foreach ($salesComp as $key => $comp) {
      $ob_accounts['t_amount'][$comp->vendor_code] = $comp->amount_paid;
      $ob_accounts['t_con'][$comp->vendor_code] = $comp->concession;
    }

    $this->db_readonly->select("ifnull(sm.total_amount,0) as ob_fees_payable");
    $this->db_readonly->from('sales_master sm');
    $this->db_readonly->where('sm.acad_year_id',$this->yearId);
    $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d")<',$fromDate);
    $obBalnce = $this->db_readonly->get()->row();
    $ob = (!empty($obBalnce)) ? $obBalnce->ob_fees_payable : 0;

    return array('ob'=>$ob,'account_ob'=>$ob_accounts);

  }

  public function get_trans_details_by_date_wise($from_date, $to_date){
      $fromDate =  date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      // $fromDate = '2020-08-25';
      // $toDate = '2020-08-28';

      $this->db_readonly->select("ft.id as transId, ft.amount_paid, ftic.id as fee_transaction_id, date_format(ft.paid_datetime,'%d-%m-%Y') as date, ft.receipt_number, fbc.name as comp_name, ftic.amount_paid as compAmount, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, CONCAT(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as classSection, ftp.payment_type, ftp.remarks")
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id')
      ->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id')
      ->join('student_admission sd','ft.student_id=sd.id')
      ->join('student_year sy',"sd.id=sy.student_admission_id")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')

      ->where('ft.soft_delete !=',1)
      ->where('ftp.reconciliation_status !=',3)
      ->where('ft.status','SUCCESS')
      // ->where('ftp.reconciliation_status !=',1)
      ->order_by('paid_datetime','desc');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $result = $this->db_readonly->get()->result();
      $tranData = [];
      foreach ($result as $key => $val) {
        if (!array_key_exists($val->transId,$tranData)) {
          $rData = new stdClass();
          $rData->transId = $val->transId;
          $rData->total_amount = $val->amount_paid;
          $rData->receipt_date = $val->date;
          $rData->receipt_number = $val->receipt_number;
          $rData->student_name = $val->student_name;
          $rData->classSection = $val->classSection;
          $rData->remarks = $val->remarks;
          $rData->payment_type = $this->_get_PaymentValue($val->payment_type);
          $rData->comp = array();
          $tranData[$val->transId] = $rData;
        }
        if ($val->fee_transaction_id) {
         $tranData[$val->transId]->comp[$val->fee_transaction_id] = array('comp_name' => $val->comp_name, 'comp_amount' => $val->compAmount);
        }
      }
      return $tranData;
    }


    public function get_day_books_details_headersv1($fee_type_id){
      $this->db_readonly->select('fbc.id as bpCompId, fbc.name, fb.name as blueprintName, fb.id as bpId');
      $this->db_readonly->from('feev2_blueprint_components fbc');
      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fbc.feev2_blueprint_id',$fee_type_id);
      }
      $this->db_readonly->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id');
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      // $this->db_readonly->where('fb.acad_year_id',$this->yearId);
      $headers_fees =  $this->db_readonly->get()->result();

      $headers_sales = array();
      if (in_array("sales",$fee_type_id) || in_array('All',$fee_type_id)) {
      $headers_sales = $this->db_readonly->select("concat('-1',ipv.id) as bpCompId, ipv.name, 'Sales' as blueprintName, '-3' as bpId ")
        ->from('inventory_product_variant ipv')
        ->get()->result();
      }

      $headers_application =[];
      if (in_array("application",$fee_type_id) || in_array('All',$fee_type_id) ) {
        $headers_application[] = (object) array('bpCompId'=>'-1','name'=>'Applications','blueprintName'=>'Applications','bpId'=>'-2');
      }
      $mergeArry = array_merge($headers_fees, $headers_sales, $headers_application);

      $headers = [];
      foreach ($mergeArry as $key => $val) {
        $headers[$val->blueprintName][] = $val;
      }
      return $headers;
    }

    public function get_day_books_details_fees_txv1_old($fee_type_id,$from_date,$to_date, $classId,$paymentModes){

      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("ft.id as transId, sum(ftic.amount_paid) as amount_paid, ftic.blueprint_component_id as compId");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->where('ft.soft_delete !=',1);
      $this->db_readonly->where('ft.status','SUCCESS');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fbc.feev2_blueprint_id',$fee_type_id);
      }
      $this->db_readonly->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id');
      $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id');
      $this->db_readonly->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id');
      $this->db_readonly->where('fb.acad_year_id',$this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      $this->db_readonly->group_by('ft.id');
      $this->db_readonly->group_by('fbc.id');
      $transComp = $this->db_readonly->get()->result();

      $this->db_readonly->select("ft.id as transId, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, receipt_number, fb.receipt_book_id, ifnull(c.class_name,'') as class_name, ifnull(cs.section_name,'') as sectionName, ftp.reconciliation_status, ftp.bank_name, date_format(ftp.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, ftp.cheque_dd_nb_cc_dd_number, ftp.recon_submitted_on, ftp.payment_type, ifnull(ft.fine_amount,'0') as fine_amount, ifnull(ft.discount_amount,'0') as discount_amount, ft.concession_amount, fb.id as bpId, ftp.remarks");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->where('ft.soft_delete !=',1);
      $this->db_readonly->where('ft.status','SUCCESS');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }

      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_cohort_student fcs','fcs.id=fss.feev2_cohort_student_id');
      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type_id);
      }
      if ($paymentModes) {
        $this->db_readonly->where_in('ftp.payment_type',$paymentModes);
      }
      $this->db_readonly->join('feev2_blueprint fb','fb.id=fcs.blueprint_id');
      $this->db_readonly->where('fb.acad_year_id',$this->yearId);
      $this->db_readonly->join('student_admission sd','ft.student_id=sd.id');
      // $this->db_readonly->where('sd.admission_status','2');
      // $this->db_readonly->where('sy.promotion_status!=','4');
      $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id');
      $this->db_readonly->where('sy.acad_year_id',$this->yearId);
      $this->db_readonly->join('class c','sy.class_id=c.id');
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $this->db_readonly->join('class_section cs','cs.id=sy.class_section_id','left');
      // $this->db_readonly->order_by('ft.paid_datetime','desc');
      $this->db_readonly->order_by('ft.receipt_number','asc');
      $transStd = $this->db_readonly->get()->result();


      foreach ($transStd as $key => &$val) {
        if (!empty($transComp)) {
           foreach ($transComp as $key => $comp) {
            if ($val->transId == $comp->transId) {
              $val->Components[$comp->compId] = $comp->amount_paid;
            }
          }
        }

      }
    // echo "<pre>"; print_r($transStd); die();
      return $transStd;
    }

    public function get_day_books_details_fees_txv1($fee_type_id,$from_date,$to_date, $classId, $paymentModes, $include_delete, $admission_type){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("ft.id as transId, ft.student_id,  date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, receipt_number, date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, ifnull(ft.fine_amount,'0') as fine_amount, ifnull(ft.discount_amount,'0') as discount_amount, ft.concession_amount, ifnull(ft.adjustment_amount,0) as adjustment_amount, (case when ft.soft_delete = 1 then 0 else ft.amount_paid end) as amountPaid, ft.soft_delete, ftp.reconciliation_status, ftp.bank_name, date_format(ftp.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, ftp.cheque_dd_nb_cc_dd_number, ftp.recon_submitted_on, ftp.payment_type,ftp.remarks, ifnull(ft.loan_provider_charges,0) as loan_provider_charges, ft.acad_year_id");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->where('ft.status','SUCCESS');
      if ($include_delete == 0) {
        $this->db_readonly->where('ft.soft_delete!=1');
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($paymentModes) {
        $this->db_readonly->where_in('ftp.payment_type',$paymentModes);
      }
      $trans = $this->db_readonly->get()->result();
      // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
      $transIds = [];
      foreach ($trans as $key => $val) {
        $transIds[$val->transId] = $val->transId;
      }
      if (empty($transIds)) {
        return array();
      }
       $this->db_readonly->select("sum(ftic.amount_paid) as amount_paid, ftic.blueprint_component_id as compId, ftic.fee_transaction_id as transId, fi.name as ins_name");
      $this->db_readonly->from('feev2_transaction_installment_component ftic');
      $this->db_readonly->where_in('ftic.fee_transaction_id',$transIds);
      $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id');
      $this->db_readonly->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id');
      $this->db_readonly->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id');
      // $this->db_readonly->where('fb.acad_year_id',$this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fb.id',$fee_type_id);
      }
      $this->db_readonly->group_by('ftic.fee_transaction_id');
      $this->db_readonly->group_by('ftic.blueprint_installments_id');
      $this->db_readonly->group_by('fb.id');
      $insData = $this->db_readonly->get()->result();

      $this->db_readonly->select("sum(ftic.amount_paid) as amount_paid, ftic.blueprint_component_id as compId, fb.id as bpId, ftic.fee_transaction_id as transId, fb.name as fee_type");
      $this->db_readonly->from('feev2_transaction_installment_component ftic');
      $this->db_readonly->where_in('ftic.fee_transaction_id',$transIds);
      $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id=fbc.id');
      $this->db_readonly->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id');
      // $this->db_readonly->where('fb.acad_year_id',$this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fb.id',$fee_type_id);
      }
      $this->db_readonly->group_by('ftic.fee_transaction_id');
      $this->db_readonly->group_by('fbc.id');
      $this->db_readonly->group_by('ftic.blueprint_component_id');
      $this->db_readonly->group_by('fb.id');
      $transComp = $this->db_readonly->get()->result();
// echo "<pre>"; print_r($this->db_readonly->last_query()); die();
      foreach ($transComp as $key => &$trComp) {
        foreach ($trans as $key => $value) {
          if ($trComp->transId == $value->transId) {
            $trComp->transId = $value->transId;
            $trComp->receipt_date = $value->receipt_date;
            $trComp->receipt_number = $value->receipt_number;
            $trComp->key = $value->receipt_number;
            $trComp->reconciliation_status = $value->reconciliation_status;
            $trComp->bank_name = $value->bank_name;
            $trComp->cheque_or_dd_date = $value->cheque_or_dd_date;
            $trComp->cheque_dd_nb_cc_dd_number = $value->cheque_dd_nb_cc_dd_number;
            $trComp->recon_submitted_on = $value->recon_submitted_on;
            $trComp->payment_type = $value->payment_type;
            $trComp->fine_amount = $value->fine_amount;
            $trComp->loan_provider_charges = $value->loan_provider_charges;
            $trComp->discount_amount = $value->discount_amount;
            $trComp->concession_amount = $value->concession_amount;
            $trComp->adjustment_amount = $value->adjustment_amount;
            $trComp->remarks = $value->remarks;
            $trComp->soft_delete = $value->soft_delete;
            $trComp->student_id = $value->student_id;
            $trComp->amountPaid = $value->amountPaid;
          }
        }
      }

      $transStdIds = [];
      foreach ($transComp as $key => $tr) {
        $transStdIds[$tr->transId] = $tr->transId;
      }


      if (!empty($transStdIds)) {
        $this->db_readonly->select("ft.id as transId, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,  ifnull(c.class_name,'') as class_name, ifnull(cs.section_name,'') as sectionName, sa.id as student_id, ft.acad_year_id, ft.collected_by, ifnull(sem.sem_name,'NA') as semester, fss.total_fee, sa.admission_no ")
        ->from('feev2_transaction ft')
        ->where_in('ft.id',$transStdIds)
        ->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
        ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->join('student_year sy',"ft.student_id=sy.student_admission_id and fb.acad_year_id=sy.acad_year_id")
        ->join('student_admission sa','sy.student_admission_id = sa.id')
        ->where('sy.promotion_status!=','JOINED')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','cs.id=sy.class_section_id','left')
        ->join('semester sem','sy.semester=sem.id','left');
        if ($classId) {
          $this->db_readonly->where_in('c.id',$classId);
        }
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($admission_type != 0) {
          $this->db_readonly->where('sy.admission_type',$admission_type);
        }
        $this->db_readonly->group_by('ft.id');
        $students = $this->db_readonly->get()->result();
        // echo "<pre>"; print_r($students); die();
        foreach ($students as $key => &$val) {
          foreach ($transComp as $key => $comp) {
            if ($val->transId == $comp->transId) {
              $val->Components[$comp->compId] = ($comp->soft_delete == 1) ? 0 : $comp->amount_paid;
              $val->receipt_date = $comp->receipt_date;
              $val->receipt_number = $comp->receipt_number;
              $val->key = $comp->receipt_number;
              $val->reconciliation_status = ($comp->soft_delete == 1) ? '' : $comp->reconciliation_status;
              $val->bank_name = ($comp->soft_delete == 1) ? '' : $comp->bank_name;
              $val->cheque_or_dd_date = ($comp->soft_delete == 1) ? '' : $comp->cheque_or_dd_date;
              $val->cheque_dd_nb_cc_dd_number = ($comp->soft_delete == 1) ? '' : $comp->cheque_dd_nb_cc_dd_number;
              $val->recon_submitted_on = ($comp->soft_delete == 1) ? '' : $comp->recon_submitted_on;
              $val->payment_type = ($comp->soft_delete == 1) ? '' : $comp->payment_type;
              $val->fine_amount = ($comp->soft_delete == 1) ? 0 : $comp->fine_amount;
              $val->loan_provider_charges = ($comp->soft_delete == 1) ? 0 : $comp->loan_provider_charges;
              $val->discount_amount = ($comp->soft_delete == 1) ? 0 : $comp->discount_amount;
              $val->concession_amount = ($comp->soft_delete == 1) ? 0 : $comp->concession_amount;
              $val->adjustment_amount = ($comp->soft_delete == 1) ? 0 : $comp->adjustment_amount;
              $val->bpId = $comp->bpId;
              $val->remarks = ($comp->soft_delete == 1) ? '' : $comp->remarks;
              $val->soft_delete = $comp->soft_delete;
              $val->fee_type = $comp->fee_type;
              $val->collected_name = $this->_getAvatarNameById($val->collected_by);
              $val->amount_paid = ($comp->soft_delete == 1) ? 0 : $comp->amountPaid;
            }
          }
          $val->ins_name ='';
          foreach ($insData as $key => $ins) {
            if ($val->transId == $ins->transId) {
              $val->ins_name .= $ins->ins_name .'='. $ins->amount_paid.'<br>';
            }
          }
        }
        return $students;
      }else{
        return array();
      }

    }

    public function get_day_books_details_fees_summary($fee_type_id,$from_date,$to_date, $classId, $paymentModes, $include_delete,$admission_type, $recon){

      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $reconTrue = 'case when ftp.reconciliation_status = 0 then date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate.'" and "'.$toDate.'" else  date_format(ftp.recon_submitted_on,"%Y-%m-%d") BETWEEN "'.$fromDate.'" and "'.$toDate.'" end';
      $reconFalse = 'date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"';

      $this->db_readonly->select("ft.id as transId, ft.student_id, ft.fee_student_schedule_id,  date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, receipt_number, ifnull(ft.fine_amount,0) as fine_amount, ifnull(ft.discount_amount,0) as discount_amount, ifnull(ft.concession_amount,0) as concession_amount, ifnull(ft.adjustment_amount,0) as adjustment_amount, (case when ft.soft_delete = 1 then 0 else ft.amount_paid end) as amountPaid, ft.soft_delete, ftp.reconciliation_status, ifnull(ftp.bank_name,'') as bank_name, date_format(ftp.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, ifnull(ftp.cheque_dd_nb_cc_dd_number,'') as cheque_dd_nb_cc_dd_number, date_format(ftp.recon_submitted_on,'%d-%m-%Y') as recon_submitted_on, ftp.payment_type, ftp.remarks, ifnull(ft.loan_provider_charges,0) as loan_provider_charges, ft.acad_year_id, fb.name as fee_type, fb.id as bpId, fcs.id as std_cohort_student_id, ft.collected_by, ifnull(ft.refund_amount,0) as refund_amount");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id');
      $this->db_readonly->join('feev2_blueprint fb','fcs.blueprint_id=fb.id');
      $this->db_readonly->where('ft.status','SUCCESS');
      // $this->db_readonly->join('student_year sy',"fcs.student_id=sy.student_admission_id and fb.acad_year_id=sy.acad_year_id");
      // $this->db_readonly->join('class c','sy.class_id=c.id');
      if ($include_delete == 0) {
        $this->db_readonly->where('ft.soft_delete!=1');
      }
      // if ($fromDate && $toDate) {
      //   $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      // }
      if ($paymentModes) {
        $this->db_readonly->where_in('ftp.payment_type',$paymentModes);
      }
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }

      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fb.id',$fee_type_id);
      }
      $this->db_readonly->get();
      $lastQuery = $this->db_readonly->last_query();
      if ($recon) {
        $lastQuery.=' and '.$reconTrue;
      }else{
        $lastQuery.=' and '.$reconFalse;
      }

      $trans = $this->db_readonly->query($lastQuery)->result();
      // $trans = $this->db_readonly->get()->result();


      $transIds = [];
      $stdCohortStudentIds = [];
      foreach ($trans as $key => $val) {
        $transIds[$val->transId] = $val->transId;
        $stdCohortStudentIds[$val->std_cohort_student_id] = $val->std_cohort_student_id;
      }
      if (empty($transIds)) {
        return array();
      }

      $this->db_readonly->select("CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,  ifnull(c.class_name,'') as class_name, ifnull(cs.section_name,'') as sectionName, sa.id as student_id,  ifnull(sem.sem_name,'NA') as semester,sa.id as student_id, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, p.aadhar_no as f_aadhar_no, p.pan_number as f_pan_number, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, p1.aadhar_no as m_aadhar_no, p1.pan_number as m_pan_number,sa.admission_no, ifnull(sa.enrollment_number,'NA') as enrollment_number")
      ->from('feev2_cohort_student fcs')
      ->where_in('fcs.id',$stdCohortStudentIds)
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('student_year sy',"fcs.student_id=sy.student_admission_id and fb.acad_year_id=sy.acad_year_id")
      ->join('student_admission sa','sy.student_admission_id = sa.id')
      ->where('sy.promotion_status!=','JOINED')
      ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
      ->join("student_relation sr1", "sr1.std_id=sa.id and sr1.relation_type='Mother'")
      ->join("parent p", "p.id=sr.relation_id")
      ->join("parent p1", "p1.id=sr1.relation_id")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','cs.id=sy.class_section_id','left')
      ->join('semester sem','sy.semester=sem.id','left');
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($admission_type != 0) {
        $this->db_readonly->where('sy.admission_type',$admission_type);
      }
      $students = $this->db_readonly->get()->result();

      $this->db_readonly->select('ftic.fee_transaction_id as transId, fi.name as ins_name, ftic.blueprint_component_id as compId, sum(ftic.amount_paid) as amount_paid');
      $this->db_readonly->from('feev2_transaction_installment_component ftic');
      $this->db_readonly->where_in('ftic.fee_transaction_id',$transIds);
      $this->db_readonly->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id');
      $this->db_readonly->group_by('ftic.fee_transaction_id');
      $this->db_readonly->group_by('ftic.blueprint_installments_id');
      $insData = $this->db_readonly->get()->result();

      $this->db_readonly->select('source_id, settlement_details_json, tx_payment_mode');
      $this->db_readonly->from('online_payment_master opm');
      $this->db_readonly->where_in('opm.source_id',$transIds);
      $online_payment = $this->db_readonly->get()->result();

      if(!empty($online_payment)){
        foreach ($online_payment as $key => $val) {
          $settlemntJson = json_decode($val->settlement_details_json);
          if (!empty($settlemntJson)) {
            foreach ($settlemntJson as $key => $splitJson) {
              $val->cheque_dd_nb_cc_dd_number = $splitJson->settlement_split[0]->bank_reference;
            }
          }else{
            $val->cheque_dd_nb_cc_dd_number = '';
          }
        }
      }
      foreach ($trans as $key => &$val) {
        $val->reconciliation_status = ($val->soft_delete == 1) ? '' : $val->reconciliation_status;
        $val->bank_name = ($val->soft_delete == 1) ? '' : $val->bank_name;
        $val->cheque_or_dd_date = ($val->soft_delete == 1) ? '' : $val->cheque_or_dd_date;
        $val->cheque_dd_nb_cc_dd_number = ($val->soft_delete == 1) ? '' : $val->cheque_dd_nb_cc_dd_number;
        $val->recon_submitted_on = ($val->soft_delete == 1) ? '' : $val->recon_submitted_on;
        $val->payment_type = ($val->soft_delete == 1) ? '' : $val->payment_type;
        $val->fine_amount = ($val->soft_delete == 1) ? 0 : $val->fine_amount;
        $val->loan_provider_charges = ($val->soft_delete == 1) ? 0 : $val->loan_provider_charges;
        $val->discount_amount = ($val->soft_delete == 1) ? 0 : $val->discount_amount;
        $val->concession_amount = ($val->soft_delete == 1) ? 0 : $val->concession_amount;
        $val->adjustment_amount = ($val->soft_delete == 1) ? 0 : $val->adjustment_amount;
        $val->refund_amount = $val->refund_amount;
        $val->bpId = $val->bpId;
        $val->remarks = ($val->soft_delete == 1) ? '' : $val->remarks;
        $val->soft_delete = $val->soft_delete;
        $val->fee_type = $val->fee_type;
        $val->collected_name = $this->_getAvatarNameById($val->collected_by);
        $val->amount_paid = ($val->soft_delete == 1) ? 0 : $val->amountPaid;
        $val->student_name = '';
        foreach ($students as $key => $std){
          if ($val->student_id == $std->student_id) {
            $val->student_name = $std->student_name;
            $val->admission_no = $std->admission_no;
            $val->enrollment_number = $std->enrollment_number;
            $val->class_name = $std->class_name;
            $val->sectionName = $std->sectionName;
            $val->semester = $std->semester;
            $val->f_name = $std->fName;
            $val->m_name = $std->mName;
            $val->f_pan_number = $std->f_pan_number;
            $val->f_aadhar_no = $std->f_aadhar_no;
            $val->m_aadhar_no = $std->m_aadhar_no;
          }
        }
        $val->ins_name ='';
        foreach ($insData as $key => $ins) {
          if ($val->transId == $ins->transId) {
            $val->Components[$ins->compId] = ($val->soft_delete == 1) ? 0 : $val->amountPaid;
            $val->ins_name .= $ins->ins_name .'='. $ins->amount_paid.'<br>';
          }
        }
        foreach ($online_payment as $key => $value) {
          if ($val->transId == $value->source_id) {
            $val->cheque_dd_nb_cc_dd_number = $value->cheque_dd_nb_cc_dd_number;
            $val->online_tx_payment_mode = $value->tx_payment_mode;
          }
        }
      }
      $transArry = [];
      foreach ($trans as $key => $tranVal) {
          if (!empty($tranVal->student_name)) {
              array_push($transArry, $tranVal);
          }
      }
      // echo "<pre>"; print_r($trans); die();


      return $transArry;

    }

    public function get_day_books_details_fees_summary1($fee_type_id,$from_date,$to_date, $classId, $paymentModes, $include_delete,$admission_type){
       $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("ft.id as transId, ft.student_id, ft.fee_student_schedule_id,  date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, receipt_number, ifnull(ft.fine_amount,0) as fine_amount, ifnull(ft.discount_amount,0) as discount_amount, ifnull(ft.concession_amount,0) as concession_amount, ifnull(ft.adjustment_amount,0) as adjustment_amount, (case when ft.soft_delete = 1 then 0 else ft.amount_paid end) as amountPaid, ft.soft_delete, ftp.reconciliation_status, ifnull(ftp.bank_name,'') as bank_name, date_format(ftp.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, ifnull(ftp.cheque_dd_nb_cc_dd_number,'') as cheque_dd_nb_cc_dd_number, ftp.recon_submitted_on, ftp.payment_type, ftp.remarks, ifnull(ft.loan_provider_charges,0) as loan_provider_charges, ft.acad_year_id, fb.name as fee_type, fb.id as bpId, fcs.id as std_cohort_student_id, ft.collected_by, ifnull(ft.refund_amount,0) as refund_amount");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id');
      $this->db_readonly->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id');
      $this->db_readonly->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id');
      $this->db_readonly->join('feev2_blueprint fb','fcs.blueprint_id=fb.id');
      $this->db_readonly->where('ft.status','SUCCESS');
      // $this->db_readonly->join('student_year sy',"fcs.student_id=sy.student_admission_id and fb.acad_year_id=sy.acad_year_id");
      // $this->db_readonly->join('class c','sy.class_id=c.id');
      if ($include_delete == 0) {
        $this->db_readonly->where('ft.soft_delete!=1');
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($paymentModes) {
        $this->db_readonly->where_in('ftp.payment_type',$paymentModes);
      }
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }

      // if ($classId) {
      //   $this->db_readonly->where_in('c.id',$classId);
      // }

      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fb.id',$fee_type_id);
      }
      $trans = $this->db_readonly->get()->result();

      $transIds = [];
      $stdCohortStudentIds = [];
      foreach ($trans as $key => $val) {
        $transIds[$val->transId] = $val->transId;
        $stdCohortStudentIds[$val->std_cohort_student_id] = $val->std_cohort_student_id;
      }
      if (empty($transIds)) {
        return array();
      }

      $this->db_readonly->select("CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,  ifnull(c.class_name,'') as class_name, ifnull(cs.section_name,'') as sectionName, sa.id as student_id,  ifnull(sem.sem_name,'NA') as semester,sa.id as student_id, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, ifnull(p.aadhar_no,'') as f_aadhar_no, ifnull(p.pan_number,'') as f_pan_number, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, ifnull(p1.aadhar_no,'') as m_aadhar_no, ifnull(p1.pan_number,'') as m_pan_number, p.id as father_id, CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS address, sa.admission_no, ifnull(sa.enrollment_number,'NA') as enrollment_number")
      ->from('feev2_cohort_student fcs')
      ->where_in('fcs.id',$stdCohortStudentIds)
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('student_year sy',"fcs.student_id=sy.student_admission_id and fb.acad_year_id=sy.acad_year_id")
      ->join('student_admission sa','sy.student_admission_id = sa.id')
      ->where('sy.promotion_status!=','JOINED')
      ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
      ->join("student_relation sr1", "sr1.std_id=sa.id and sr1.relation_type='Mother'")
      ->join("parent p", "p.id=sr.relation_id")
      ->join("parent p1", "p1.id=sr1.relation_id")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','cs.id=sy.class_section_id','left')
      ->join('semester sem','sy.semester=sem.id','left')
      ->join('address_info add',"add.stakeholder_id=p.id and add.avatar_type=2 and add.address_type=1",'left');
      // ->where('add.avatar_type','2') //Parent Address
      // ->where('add.address_type','1'); //Permanent Address
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($admission_type != 0) {
        $this->db_readonly->where('sy.admission_type',$admission_type);
      }
      $students = $this->db_readonly->get()->result();


      $this->db_readonly->select('ftic.fee_transaction_id as transId, fi.name as ins_name, ftic.blueprint_component_id as compId, sum(ftic.amount_paid) as amount_paid');
      $this->db_readonly->from('feev2_transaction_installment_component ftic');
      $this->db_readonly->where_in('ftic.fee_transaction_id',$transIds);
      $this->db_readonly->join('feev2_installments fi','ftic.blueprint_installments_id=fi.id');
      $this->db_readonly->group_by('ftic.fee_transaction_id');
      $this->db_readonly->group_by('ftic.blueprint_installments_id');
      $insData = $this->db_readonly->get()->result();

      foreach ($trans as $key => &$val) {
        $val->reconciliation_status = ($val->soft_delete == 1) ? '' : $val->reconciliation_status;
        $val->bank_name = ($val->soft_delete == 1) ? '' : $val->bank_name;
        $val->cheque_or_dd_date = ($val->soft_delete == 1) ? '' : $val->cheque_or_dd_date;
        $val->cheque_dd_nb_cc_dd_number = ($val->soft_delete == 1) ? '' : $val->cheque_dd_nb_cc_dd_number;
        $val->recon_submitted_on = ($val->soft_delete == 1) ? '' : $val->recon_submitted_on;
        $val->payment_type = ($val->soft_delete == 1) ? '' : $val->payment_type;
        $val->fine_amount = ($val->soft_delete == 1) ? 0 : $val->fine_amount;
        $val->loan_provider_charges = ($val->soft_delete == 1) ? 0 : $val->loan_provider_charges;
        $val->discount_amount = ($val->soft_delete == 1) ? 0 : $val->discount_amount;
        $val->concession_amount = ($val->soft_delete == 1) ? 0 : $val->concession_amount;
        $val->adjustment_amount = ($val->soft_delete == 1) ? 0 : $val->adjustment_amount;
        $val->refund_amount = $val->refund_amount;
        $val->bpId = $val->bpId;
        $val->remarks = ($val->soft_delete == 1) ? '' : $val->remarks;
        $val->soft_delete = $val->soft_delete;
        $val->fee_type = $val->fee_type;
        $val->collected_name = $this->_getAvatarNameById($val->collected_by);
        $val->amount_paid = ($val->soft_delete == 1) ? 0 : $val->amountPaid;
        $val->student_name = '';
        $val->admission_no = '';
        foreach ($students as $key => $std){
          if ($val->student_id == $std->student_id) {
            $val->student_name = $std->student_name;
            $val->admission_no = $std->admission_no;
            $val->enrollment_number = $std->enrollment_number;
            $val->class_name = $std->class_name;
            $val->sectionName = $std->sectionName;
            $val->semester = $std->semester;
            $val->fName = $std->fName;
            $val->mName = $std->mName;
            $val->admission_no = $std->admission_no;
            $val->f_pan_number = $std->f_pan_number;
            $val->m_pan_number = $std->m_pan_number;
            $val->f_aadhar_no = $std->f_aadhar_no;
            $val->m_aadhar_no = $std->m_aadhar_no;
            $val->address = $std->address;
          }
        }
        $val->ins_name ='';
        foreach ($insData as $key => $ins) {
          if ($val->transId == $ins->transId) {
            $val->Components[$ins->compId] = ($val->soft_delete == 1) ? 0 : $val->amountPaid;
            $val->ins_name .= $ins->ins_name .'='. $ins->amount_paid.'<br>';
          }
        }
      }
      $transArry = [];
      foreach ($trans as $key => $tranVal) {
          if (!empty($tranVal->student_name)) {
              array_push($transArry, $tranVal);
          }
      }

      return $transArry;
    }

    public function get_day_books_details_sale_tx1($from_date,$to_date,$paymentModes, $include_delete){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("sm.id as transId, sum(st.amount) as amount_paid, concat('-1',st.inventory_product_variant_id) as compId");
      $this->db_readonly->from('sales_master sm');
      if ($include_delete == 0) {
        $this->db_readonly->where('sm.soft_delete!=1');
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->join('sales_transactions st','sm.id=st.sales_master_id');
      $this->db_readonly->group_by('st.sales_master_id');
      // $this->db_readonly->group_by('st.inventory_product_id');
      $this->db_readonly->group_by('st.inventory_product_variant_id');
      $this->db_readonly->group_by('sm.id');
      $transComp = $this->db_readonly->get()->result();

      $this->db_readonly->select("sm.id as transId, (case when(c.class_name IS NULL) then ifnull(sm.class_name, 'NA') else c.class_name  end) as class_name, '' as sectionName, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date, receipt_no as receipt_number, ipc.receipt_book_id, sales_type,  parent_name, (case when sm.student_id = 0 then sm.student_name else CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) end) as student_name, sm.payment_type, ifnull(bank_name,'') as bank_name, bank_branch,cheque_dd_number as cheque_dd_nb_cc_dd_number, recon_submitted_on,cheque_dd_date as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '0' as adjustment_amount, '-3' as bpId, '0' as reconciliation_status, sm.remarks, (case when sm.soft_delete = 1 then 0 else sm.total_amount end) as amountPaid, sm.soft_delete, '0' as loan_provider_charges, ifnull(sem.sem_name,'NA') as semester, '0' as refund_amount,  concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, ifnull(p.aadhar_no,'') as f_aadhar_no, ifnull(p.pan_number,'') as f_pan_number, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, ifnull(p1.aadhar_no,'') as m_aadhar_no, ifnull(p1.pan_number,'') as m_pan_number,'' as address, sd.admission_no, sm.created_by, ifnull(sd.enrollment_number,'NA') as enrollment_number");
      $this->db_readonly->from('sales_master sm');
      if ($include_delete == 0) {
        $this->db_readonly->where('sm.soft_delete!=1');
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      if ($paymentModes) {
        $this->db_readonly->where_in('sm.payment_type',$paymentModes);
      }
      $this->db_readonly->join('inventory_product_category ipc','sm.category_id=ipc.id');
      $this->db_readonly->join('student_admission sd',"sm.student_id=sd.id",'left');
      $this->db_readonly->join('student_year sy',"sd.id=sy.student_admission_id and sy.acad_year_id=sm.acad_year_id",'left');
      $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'",'left');
      $this->db_readonly->join("student_relation sr1", "sr1.std_id=sd.id and sr1.relation_type='Mother'",'left');
      $this->db_readonly->join("parent p", "p.id=sr.relation_id",'left');
      $this->db_readonly->join("parent p1", "p1.id=sr1.relation_id",'left');

      // $this->db_readonly->where('sy.promotion_status!=','4');
      $this->db_readonly->join('class c','sy.class_id=c.id','left');
      $this->db_readonly->join('semester sem','sy.semester=sem.id','left');
      // $this->db_readonly->order_by('sm.receipt_date','desc');
      $this->db_readonly->group_by('sm.id');
      $this->db_readonly->order_by('sm.receipt_no','asc');
      $transStd = $this->db_readonly->get()->result();
      foreach ($transStd as $key => &$val) {
        foreach ($transComp as $key => $comp) {
          if ($val->transId == $comp->transId) {
            $val->Components[$comp->compId] = ($val->soft_delete == 1) ? 0 : $comp->amount_paid;
            $val->amount_paid = ($val->soft_delete == 1) ? 0 : $val->amountPaid;
            $val->fee_type = 'Sales';
            $val->total_fee = '0';
            $val->collected_name = $this->_getAvatarNameById($val->created_by);
          }
        }
      }
      return $transStd;
    }

    public function get_day_books_details_appliation_tx1($from_date, $to_date, $paymentModes){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("af.id as transId,  (case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amount_paid, '-1' as compId");
      $this->db_readonly->from('admission_forms af');
      $this->db_readonly->join('admission_status as','af.id=as.af_id');
      $this->db_readonly->where('as.payment_status','SUCCESS');
      // $this->db_readonly->where('af.academic_year_applied_for',$this->yearId);
      $this->db_readonly->where('af.application_no !=','');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(af.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->join('admission_transaction at',"af.id=at.af_id  and af.filled_by !=0 ",'left');
      $this->db_readonly->join('online_application_fee_payment_master opf',"af.id=opf.source_id and opf.tx_response_code=0",'left');
      $transComp  = $this->db_readonly->get()->result();

      $this->db_readonly->select("af.id as transId, date_format(af.created_on,'%d-%m-%Y') as receipt_date, (case when af.receipt_number IS NULL then  af.application_no else af.receipt_number end) as receipt_number,  ifnull(af.std_name,'') as student_name, ifnull(af.grade_applied_for,'') as class_name, '' as sectionName, (case when af.filled_by = 0 then 10 when af.filled_by !=0 then at.payment_type end) as payment_type, at.bank_name, at.branch_name as bank_branch, at.cheque_or_dd_number as cheque_dd_nb_cc_dd_number, date_format( at.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '0' as adjustment_amount, '-2' as bpId, '0' as reconciliation_status, (case when af.filled_by = 0 then '' when af.filled_by !=0 then at.remarks end) as remarks,(case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amountPaid, '0' as soft_delete, '0' as loan_provider_charges, 'NA' as semester, '0' as refund_amount, ifnull(af.f_name,'') AS fName, ifnull(af.m_name,'') AS mName, ifnull(af.father_aadhar,'') as f_aadhar_no, ifnull(af.f_pan_number,'') as f_pan_number,  ifnull(af.mother_aadhar,'') as m_aadhar_no, '' as address, '' as admission_no, 'NA' as enrollment_number");
      $this->db_readonly->from('admission_forms af');
      $this->db_readonly->join('admission_status as','af.id=as.af_id');
      $this->db_readonly->where('as.payment_status','SUCCESS');
      // $this->db_readonly->where('af.academic_year_applied_for',$this->yearId);
      $this->db_readonly->where('af.application_no !=','');
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(af.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }

      $this->db_readonly->join('admission_transaction at',"af.id=at.af_id  and af.filled_by !=0 ",'left');
      $this->db_readonly->join('online_application_fee_payment_master opf',"af.id=opf.source_id and opf.tx_response_code=0",'left');
      // $this->db_readonly->join('admission_transaction at','af.id=at.af_id');
      // $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id');
      // $this->db_readonly->join('class c','sy.class_id=c.id');
      $this->db_readonly->order_by('af.application_no','asc');
      $transStd = $this->db_readonly->get()->result();

      foreach ($transStd as $key => &$val) {
        foreach ($transComp as $key => $comp) {
          if ($val->transId == $comp->transId) {
            $val->Components[$comp->compId] = $comp->amount_paid;
            $val->amount_paid = $comp->amount_paid;
            $val->fee_type = 'Application';
            $val->total_fee = '0';
          }
        }
      }

      return $transStd;
    }
  private function _getAvatarNameById($avatarId) {
    $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
        ->from('staff_master sm')
        ->join('avatar a', 'sm.id=a.stakeholder_id')
        ->where('a.avatar_type', '4') // 4 avatar type staff
        ->where('a.id',$avatarId)
        ->get()->row();
    if (!empty($collected)) {
      return $collected->staffName;
    }else{
      return 'Admin';
    }
  }

  public function get_transactions_bpWisev1($from_date, $to_date, $classId){

    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));

    $this->db_readonly->select("ft.id as transId, sum(ftic.amount_paid) as amount_paid, fbc.vendor_code, ft.id as transId, sum(ftic.concession_amount) as concession");
    $this->db_readonly->from('feev2_transaction ft');
    $this->db_readonly->where('ft.soft_delete !=',1);
    $this->db_readonly->where('ft.status','SUCCESS');
    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db_readonly->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id');
    $this->db_readonly->join('feev2_blueprint_components fbc','ftic.blueprint_component_id = fbc.id');
    $this->db_readonly->group_by('fbc.vendor_code');
    $this->db_readonly->group_by('ft.id');
    $transComp = $this->db_readonly->get()->result();
    $this->db_readonly->select("ft.id as transId, sum(ft.amount_paid) as txFeePaid, CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, receipt_number, sum(fss.total_fee) as fees_payable, CONCAT(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name,");
    $this->db_readonly->from('feev2_cohort_student fcs');
    $this->db_readonly->join('feev2_student_schedule fss','fss.feev2_cohort_student_id=fcs.id');
    $this->db_readonly->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id');
   if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db_readonly->where('ft.soft_delete !=',1);
    $this->db_readonly->where('ft.status','SUCCESS');
    $this->db_readonly->join('student_admission sd',"ft.student_id=sd.id");
    $this->db_readonly->join('student_year sy',"sd.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
    // $this->db_readonly->where('sy.promotion_status!=','4');
    $this->db_readonly->join('class c',"sy.class_id=c.id and c.acad_year_id = $this->yearId and is_placeholder !=1");
    $this->db_readonly->join('class_section cs',"cs.id=sy.class_section_id and cs.is_placeholder!=1",'left');
    if ($classId) {
      $this->db_readonly->where_in('c.id',$classId);
    }
    $this->db_readonly->group_by('ft.id');
    $this->db_readonly->order_by('paid_datetime','desc');
    $transStd = $this->db_readonly->get()->result();
    $total = [];
    foreach ($transStd as $key => &$val) {
      foreach ($transComp as $key => $comp) {
        if ($val->transId == $comp->transId) {
          $val->components[$comp->vendor_code] = $comp->amount_paid;
          $val->concession[$comp->vendor_code] = $comp->concession;
          if(!array_key_exists($comp->vendor_code, $total)) {
            $total[$comp->vendor_code] = 0;
            $total['concession'] = 0;
          }
          $total[$comp->vendor_code] += $comp->amount_paid;
          $total['concession'] += $comp->concession;
        }

      }
    }

    return array('result' => $transStd, 'total' => $total);

  }
  public function get_transactions_bpWise_salesv1($from_date, $to_date){
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate =date('Y-m-d',strtotime($to_date));

    $beforeDate = date('Y-m-d', strtotime($from_date. " -1 days"));
    $this->db_readonly->select("sm.id as salesId, sum(st.amount) as amount_paid, sm.student_id, (case when ipv.vendor_code IS NOT NULL then ipv.vendor_code else 'sales' end) as vendor_code, '0' as concession");
    $this->db_readonly->from('sales_master sm');
    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db_readonly->where('sm.soft_delete!=1');
    $this->db_readonly->join('sales_transactions st','st.sales_master_id=sm.id');
    $this->db_readonly->join('inventory_product_variant ipv','st.inventory_product_variant_id=ipv.id');
    $this->db_readonly->group_by('ipv.vendor_code');
    $this->db_readonly->group_by('sm.id');
    $salesComp = $this->db_readonly->get()->result();


    $this->db_readonly->select("sm.student_id as student_id, sm.total_amount as txFeePaid, (case when c.class_name IS NOT NULL then c.class_name else 'NA' end) as class_name, (case when sm.student_name IS NOT NULL then sm.student_name else CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) end) as student_name, date_format(sm.receipt_date,'%d-%m-%Y') as receipt_date, sm.total_amount as fees_payable, sm.id as salesId, sm.receipt_no as receipt_number");
    $this->db_readonly->from('sales_master sm');
    $this->db_readonly->join('student_admission sd',"sm.student_id=sd.id",'left');
    $this->db_readonly->join('student_year sy','sd.id=sy.student_admission_id','left');
    $this->db_readonly->join('class c','sy.class_id=c.id','left');
     if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }
    $this->db_readonly->where('sm.soft_delete!=1');
    $this->db_readonly->group_by('sm.id');
    $this->db_readonly->order_by('receipt_date','desc');
    $salesStd = $this->db_readonly->get()->result();

    $total = [];
    foreach ($salesStd as $key => &$val) {
      foreach ($salesComp as $key => $comp) {
        if ($val->salesId == $comp->salesId) {
          $val->components[$comp->vendor_code] = $comp->amount_paid;
          $val->concession[$comp->vendor_code] = $comp->concession;
        }
        if(!array_key_exists($comp->vendor_code, $total)) {
          $total[$comp->vendor_code] = 0;
          $total['concession'] = 0;
        }
        $total[$comp->vendor_code] += $comp->amount_paid;
        $total['concession'] += $comp->concession;
      }
      $val->previousDateFeePaid = 0;
    }
    return array('result' => $salesStd, 'total' => $total);
   // return $salesStd;

  }

  public function get_fee_summary_student_wise_v2($payment_status, $fee_type, $classId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId, $acad_year_id, $combination,$installmentId, $admission_status, $staff_kid){
    if ($donorsId === 'Null') {
      $donorsId = 1;
    }else{
      $donorsId = $donorsId;
    }

    $this->db_readonly->select('fcs.student_id')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
    ->where('fb.acad_year_id',$this->yearId);
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    if ($fee_type) {
      $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
    }
    if ($installmentId) {
      $this->db_readonly->join('feev2_student_installments fsi',"fss.id=fsi.fee_student_schedule_id");
      $this->db_readonly->where_in('fsi.feev2_installments_id',$installmentId);
    }

    if ($payment_status) {
      $this->db_readonly->where('fss.payment_status',$payment_status);
    }
    $this->db_readonly->group_by('fcs.student_id');
    $feeQuery = $this->db_readonly->get()->result();

    if (empty($feeQuery)) {
      return array();
    }
    $studentIds = [];
    foreach ($feeQuery as $key => $val) {
      array_push($studentIds, $val->student_id);
    }

    $this->db_readonly->select('sa.id as studentId, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status');
    $this->db_readonly->from('student_admission sa');
    $this->db_readonly->group_start();
    $stdIDsChunk = array_chunk($studentIds,200);
    foreach($stdIDsChunk as $std)
    {
      $this->db_readonly->or_where_in('sa.id', $std);
    }
    $this->db_readonly->group_end();
    // $this->db_readonly->where_in('sa.id',$studentIds);
    $this->db_readonly->join('student_year sy','sa.id=sy.student_admission_id');
    // $this->db_readonly->where('sy.promotion_status!=',4);
    // $this->db_readonly->where('sy.promotion_status!=',5);
    $this->db_readonly->where('sy.acad_year_id',$this->yearId);
    $this->db_readonly->join('class c','sy.class_id=c.id');
    $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
    $this->db_readonly->order_by('c.id, cs.id, sa.first_name');
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    if ($staff_kid !='all') {
      $this->db_readonly->where('sa.has_staff',$staff_kid);
    }
    if ($acad_year_id) {
      $this->db_readonly->where_in('sa.admission_acad_year_id',$acad_year_id);
    }
     if ($classId) {
      $this->db_readonly->where_in('c.id',$classId);
    }
    if ($classSectionId) {
      $this->db_readonly->where_in('cs.id',$classSectionId);
    }
    if ($admission_type) {
      $this->db_readonly->where('sy.admission_type',$admission_type);
    }
    if ($combination) {
      $this->db_readonly->where_in('sy.combination',$combination);
    }
    if ($mediumId) {
      $this->db_readonly->where('sy.medium',$mediumId);
    }
    if ($category) {
      $this->db_readonly->where('sa.category',$category);
    }
    if ($rte_nrteId) {
      $this->db_readonly->where('sy.is_rte',$rte_nrteId);
    }
    if ($donorsId) {
      if ($donorsId == 1) {
        $this->db_readonly->where('sy.donor is null');
      }else{
        $this->db_readonly->where_in('sy.donor',$donorsId);
      }
    }
    $this->db_readonly->order_by('sa.first_name','c.id','cs.id');
    $result = $this->db_readonly->get()->result();

    $studentIDs = [];
    if ($admission_status) {
      foreach ($result as $key => $val) {
        if ($admission_status == $val->admission_status) {
          array_push($studentIDs, $val->studentId);
        }
      }
    }else{
       foreach ($result as $key => $val) {
        array_push($studentIDs, $val->studentId);
      }
    }

    return $studentIDs;
  }

  public function get_fee_summary_student_wise_v2_details($cohortStudentIds, $payment_status, $fee_type, $classId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId,$acad_year_id, $combination,$installmentId){
     // echo "<pre>"; print_r($cohortStudentIds);
    if ($donorsId === 'Null') {
      $donorsId = 1;
    }else{
      $donorsId = $donorsId;
    }
    $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, c.class_name as className, ifnull(cs.section_name,'') as sectionName, sa.admission_no, p.first_name as father_name,  ifnull(p.mobile_no,'') as father_mobile_no, sa.sts_number, sy.picture_url, sa.gender, sa.admission_acad_year_id, fss.id as schid, fss.total_fee as fee_amount, ifnull(fss.total_fee_paid,0) as paid_amount, fss.payment_status, ifnull(fss.discount,0) as discount, ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0) as concession, ifnull(fsi.total_concession_amount,0) as applied_concession, ifnull(fsi.total_concession_amount_paid,0) as received_concession, ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0) as total_concession, ifnull(fss.total_fine_amount,0) as fine_amount, ifnull(fss.total_card_charge_amount,0) as card_charge_amount, ifnull(fss.refund_amount,0) as refund_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, ifnull(fsi.installment_amount,0) as installment_amount, fsi.feev2_installments_id as insId, fsi.status as installment_status,fcs.blueprint_id, fi.installment_order as insFlag")
    ->from('feev2_student_schedule fss')
    ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
    ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
    ->where('fb.acad_year_id',$this->yearId)
    ->join('student_admission sa','fcs.student_id=sa.id')
    ->where_in('sa.id',$cohortStudentIds)
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->where('sy.acad_year_id',$this->yearId)
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id','left')
    ->join('student_relation sr',"sr.std_id=sa.id and relation_type='Father'")
    ->join('parent p','p.id=sr.relation_id');
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    if ($fee_type) {
      $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
    }
    if ($acad_year_id) {
      $this->db_readonly->where_in('sa.admission_acad_year_id',$acad_year_id);
    }
     if ($classId) {
      $this->db_readonly->where_in('c.id',$classId);
    }
    if ($classSectionId) {
      $this->db_readonly->where_in('cs.id',$classSectionId);
    }
    if ($admission_type) {
      $this->db_readonly->where('sy.admission_type',$admission_type);
    }
    if ($combination) {
      $this->db_readonly->where_in('sy.combination',$combination);
    }
    if ($mediumId) {
      $this->db_readonly->where('sy.medium',$mediumId);
    }
    if ($category) {
      $this->db_readonly->where('sa.category',$category);
    }
    if ($rte_nrteId) {
      $this->db_readonly->where('sy.is_rte',$rte_nrteId);
    }
    if ($donorsId) {
      if ($donorsId == 1) {
        $this->db_readonly->where('sy.donor is null');
      }else{
        $this->db_readonly->where('sy.donor',$donorsId);
      }
    }
    if ($installmentId) {
      $this->db_readonly->where_in('fsi.feev2_installments_id',$installmentId);
    }
    if ($payment_status) {
      $this->db_readonly->where('fss.payment_status',$payment_status);
    }
    $this->db_readonly->order_by('c.id, cs.id, sa.first_name');
    $query = $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
    $shcIds = [];
    foreach ($query as $key => $value) {
      $shcIds[] = $value->schid;
    }

    $transcation = $this->db_readonly->select("ft.id as transid, ft.student_id, ft.amount_paid, date_format(ft.paid_datetime,'%d-%m-%Y') as paid_date, ft.concession_amount, ftp.payment_type, fcs.blueprint_id, ft.receipt_number")
    ->from('feev2_transaction ft')
    ->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
    ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
    ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id and reconciliation_status !=3 and reconciliation_status !=1')
    ->where('ft.soft_delete!=',1)
    ->where_in('ft.fee_student_schedule_id',$shcIds)
    ->where('ft.status','SUCCESS')
    ->get()->result();

    $this->db_readonly->select('fi.name, fi.id, fb.id as fbId, fi.installment_order as flag, fit.name as installmentsType, fb.name as blueprint_name')
    ->from('feev2_blueprint fb')
    ->where('fb.acad_year_id',$this->yearId)
    ->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id')
    ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
    ->join('feev2_installments fi','fit.id=fi.feev2_installment_type_id')
    ->order_by('fi.id');
    if ($fee_type) {
     $this->db_readonly->where_in('fb.id',$fee_type);
    }
    if ($installmentId) {
     $this->db_readonly->where_in('fi.id',$installmentId);
    }
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    $headersIns = $this->db_readonly->get()->result();

    $bpIds = [];
    foreach ($headersIns as $key => $val) {
      // $bpIds[$val->fbId]['blueprint_name']=$val->blueprint_name;
      if ($val->flag != 0) {
        $bpIds[$val->fbId][$val->flag]['name']=$val->blueprint_name;
        $bpIds[$val->fbId][$val->flag]['id']=$val->id;
        $bpIds[$val->fbId][$val->flag]['installments']=$val->name;
      }else{
        $bpIds[$val->fbId][$val->id]['name']=$val->blueprint_name;
        $bpIds[$val->fbId][$val->id]['id']=$val->id;
        $bpIds[$val->fbId][$val->id]['installments']=$val->name;
      }
    }
    $bpHeaderName = [];
    foreach ($headersIns as $key => $val) {
      $bpHeaderName[$val->fbId]['blueprint_name']=$val->blueprint_name;
    }
    $bpArry = [];
    $blueprintsTotal = [];
    foreach ($query as $key => $val) {
      if (!array_key_exists($val->blueprint_id, $blueprintsTotal)) {
        $blueprintsTotal[$val->blueprint_id] = array();
        $blueprintsTotal[$val->blueprint_id]['total_fee_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['total_balance_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['total_fee_paid_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['total_concession_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['applied_concession_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['received_concession_summary'] = 0;
      }
      if(!array_key_exists($val->stdId, $bpArry)) {
        $bpArry[$val->stdId] = array();
        $bpArry[$val->stdId]['total_fee'] = 0;
        $bpArry[$val->stdId]['total_balance'] = 0;
        $bpArry[$val->stdId]['total_concession'] = 0;
        $bpArry[$val->stdId]['total_concession_sch'] = 0;
        $bpArry[$val->stdId]['total_fee_paid'] = 0;
        $bpArry[$val->stdId]['stdId'] = $val->stdId;
        $bpArry[$val->stdId]['key'] = $key;
        $bpArry[$val->stdId]['student_name'] = $val->student_name;
        $bpArry[$val->stdId]['class_name'] = $val->class_name;
        $bpArry[$val->stdId]['className'] = $val->className;
        $bpArry[$val->stdId]['sectionName'] = $val->sectionName;
        $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
        $bpArry[$val->stdId]['parent_name'] = $val->father_name;
        $bpArry[$val->stdId]['mobile_no'] = $val->father_mobile_no;
        $bpArry[$val->stdId]['discount'] = $val->discount;
        $bpArry[$val->stdId]['fine_amount'] = $val->fine_amount;
        $bpArry[$val->stdId]['card_charge_amount'] = $val->card_charge_amount;
        $bpArry[$val->stdId]['refund_amount'] = $val->refund_amount;


        if ($val->insFlag !=0) {
          $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insFlag] = array();
        }else{
          $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insId] = array();;
        }
        // $bpArry[$val->stdId]['installments'][$val->blueprint_id][$val->insId] = array();
      }

      if ($val->insFlag !=0) {
        $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insFlag] = array(
          'installment_amount'=> $val->installment_amount,
          'installment_amount_paid'=> $val->installment_amount_paid,
          'status'=> $val->installment_status,
          'concession'=> $val->concession,
        );
      }else{
        $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insId] = array(
          'installment_amount'=> $val->installment_amount,
          'installment_amount_paid'=> $val->installment_amount_paid,
          'status'=> $val->installment_status,
          'concession'=> $val->concession,
        );
      }


      $bpArry[$val->stdId]['total_fee'] += $val->installment_amount;
      $bpArry[$val->stdId]['total_balance'] += ($val->installment_amount - $val->installment_amount_paid - $val->concession);
      $bpArry[$val->stdId]['total_concession'] += $val->concession;
      $bpArry[$val->stdId]['total_concession_sch'] = $val->total_concession;
      $bpArry[$val->stdId]['total_fee_paid'] += $val->installment_amount_paid;

      $blueprintsTotal[$val->blueprint_id]['total_fee_summary'] +=  $val->installment_amount;
      $blueprintsTotal[$val->blueprint_id]['total_balance_summary'] += ($val->installment_amount - $val->installment_amount_paid - $val->concession);
      $blueprintsTotal[$val->blueprint_id]['total_concession_summary'] += $val->concession;
      $blueprintsTotal[$val->blueprint_id]['total_fee_paid_summary'] += $val->installment_amount_paid;

      $blueprintsTotal[$val->blueprint_id]['applied_concession_summary'] += $val->applied_concession;
      $blueprintsTotal[$val->blueprint_id]['received_concession_summary'] += $val->received_concession;
    }
    // echo "<pre>"; print_r($blueprintsTotal); die();
    $fee_studentArr = [];
    foreach ($bpArry as $key => $arr) {
      array_push($fee_studentArr, $arr);
    }

    $transactions = [];
    foreach ($transcation as $key => $tran) {
      // if (!array_key_exists($tran->student_id, $transactions)) {
      //   $transactions[$tran->student_id]['transaction'][$tran->blueprint_id] = array();
      // }
      $transactions[$tran->student_id]['transaction'][$tran->blueprint_id][] = array(
        'receipt_number' => $tran->receipt_number,
        'paid_date' => $tran->paid_date,
        'amount_paid' => $tran->amount_paid,
        'concession_amount' => $tran->concession_amount,
        'payment_type' => $this->_get_PaymentValue($tran->payment_type)
      );
    }

    // usort($bpArry, function($a, $b) { return $a['key'] - $b['key']; });

    // echo "<pre>"; print_r($bpArry);
    // die();

    $header = '<thead>';
    $header .= '<tr>';
    $header .= '<th rowspan="3">#</th>';
    $header .= '<th rowspan="3">Student</th>';
    $header .= '<th rowspan="3">Class</th>';
    $header .= '<th rowspan="3">Section</th>';
    $header .= '<th rowspan="3">Admission No.</th>';
    $header .= '<th rowspan="3">Parent Name</th>';
    $header .= '<th rowspan="3">Parent Number</th>';
    $header .= '<tr>';
    $header .='<th colspan="4" style="text-align:center;">Over all Total</th>';
    foreach ($bpIds as $bpId => $val) {
      foreach ($val as $ins_id => $v) {
        $insName = $v['installments'].' - '.$v['name'];
        $header .='<th colspan="5" style="text-align:center" >'.$insName.'</th>';
      }
      $header .= '<th colspan="5" style="text-align:center;vertical-align:middle">Transactions</th>';
      // $header .= '<th rowspan="2" style="text-align:center;vertical-align:middle">Fine</th>';
      // $header .= '<th rowspan="2" style="text-align:center;vertical-align:middle">Discount</th>';
      // $header .= '<th rowspan="2" style="text-align:center;vertical-align:middle">Cardcharge</th>';
      // $header .= '<th rowspan="2" style="text-align:center;vertical-align:middle">Refund</th>';
    }
    $header .= '</tr>';

    $header .= '<tr>';
    $header .= '<th>Fee Amount</th>';
    $header .= '<th>Fee Paid</th>';
    $header .= '<th>Concession</th>';
    $header .= '<th>Balance</th>';
    foreach ($bpIds as $bpId => $val) {
      foreach ($val as $ins_id => $ins_name) {
        $header .= '<th>Fee Amount</th>';
        $header .= '<th>Collected</th>';
        $header .= '<th>Concession</th>';
        $header .= '<th>Balance</th>';
        $header .= '<th>Status</th>';
      }

      $header .= '<th>Paid Date</th>';
      $header .= '<th>Receipt Number</th>';
      $header .= '<th>Amount Paid</th>';
      $header .= '<th>Concession</th>';
      $header .= '<th>Payment Mode</th>';
    }
    $header .= '</tr>';

    $header .= '</thead>';

  // echo '<pre>'; print_r($this->db_readonly->last_query()); die();
  return array('fee_data' => $fee_studentArr, 'header' => $header,'headers'=>$bpIds,'summary'=>$blueprintsTotal,'transactions'=>$transactions,'summaryHeaderbp'=>$bpHeaderName);
  }

  /**
   * Enhanced function to get both regular and component-wise fee data
   * @param array $cohortStudentIds - Student IDs to fetch data for
   * @param string $include_components - 'yes' to include component data, 'no' for regular only
   * @param array $component_ids - Specific component IDs to fetch (optional)
   * ... other existing parameters
   */
  public function get_fee_summary_student_wise_v2_details_enhanced($cohortStudentIds, $payment_status, $fee_type, $classId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId,$acad_year_id, $combination,$installmentId, $transaction_hide_show, $include_components = 'no', $component_ids = null){

    // Call the existing regular function first
    $regular_data = $this->get_fee_summary_student_wise_v2_details_new($cohortStudentIds, $payment_status, $fee_type, $classId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId,$acad_year_id, $combination,$installmentId, $transaction_hide_show);

    // If component data is not requested, return regular data
    if($include_components !== 'yes') {
      return $regular_data;
    }

    // Fetch component-wise data
    $component_data = $this->get_student_wise_component_getStudentsForSummary_data(
      $cohortStudentIds,
      null, // selectedColumns
      $classId,
      $admission_type,
      $mediumId,
      $category,
      $donorsId,
      $created_byId,
      $classSectionId,
      $rte_nrteId,
      $acad_year_id,
      $combination,
      $installmentId,
      null, // admission_status
      null, // staff_kid
      $fee_type
    );

    // Merge regular and component data
    $enhanced_data = $regular_data;
    $enhanced_data['component_data'] = $component_data;
    $enhanced_data['has_components'] = true;

    return $enhanced_data;
  }

  public function get_fee_summary_student_wise_v2_details_new($cohortStudentIds, $payment_status, $fee_type, $classId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId,$acad_year_id, $combination,$installmentId, $transaction_hide_show, $show_component_wise = false){
     // echo "<pre>"; print_r($cohortStudentIds);
    if ($donorsId === 'Null') {
      $donorsId = 1;
    }else{
      $donorsId = $donorsId;
    }

    // Enhanced query to include component-wise data alongside regular installment data
    $this->db_readonly->select("fss.id as schid, fss.total_fee as fee_amount, ifnull(fss.total_fee_paid,0) as paid_amount, fss.payment_status, ifnull(fss.discount,0) as discount, ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0) as concession, ifnull(fsi.total_concession_amount,0) as applied_concession, ifnull(fsi.total_concession_amount_paid,0) as received_concession, ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0) as total_concession, (ifnull(fss.total_fine_amount,0) - ifnull(fss.total_fine_amount_paid,0)) as fine_amount, (ifnull(fss.total_fine_amount_paid,0)) as fine_collected, ifnull(fss.total_card_charge_amount,0) as card_charge_amount, ifnull(fss.refund_amount,0) as refund_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, ifnull(fsi.installment_amount,0) as installment_amount, fsi.feev2_installments_id as insId, fsi.status as installment_status,fcs.blueprint_id, fi.installment_order as insFlag, fcs.student_id, ifnull(fss.loan_provider_charges,0) as loan_provider_charges, ifnull(fsi.total_adjustment_amount,0) + ifnull(fsi.total_adjustment_amount_paid,0) as adjustment_applied, ifnull(fss.total_adjustment_amount,0)  + ifnull(fss.total_adjustment_amount_paid,0) as total_adjustment, ifnull(fsi.total_adjustment_amount,0)  + ifnull(fsi.total_adjustment_amount_paid,0) as adjustment, ifnull(fsi.total_fine_amount_paid,0) as fine_applied, CASE WHEN fi.end_date <= CURDATE() THEN (fsi.installment_amount - IFNULL(fsi.installment_amount_paid, 0) - IFNULL(fsi.total_concession_amount, 0) - IFNULL(fsi.total_concession_amount_paid, 0)) ELSE 0 END as due_amount")
    ->from('feev2_cohort_student fcs')
    ->where_in('fcs.student_id',$cohortStudentIds)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
    ->where('fb.acad_year_id',$this->yearId)
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id');
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    if ($fee_type) {
      $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
    }
    if ($installmentId) {
      $this->db_readonly->where_in('fsi.feev2_installments_id',$installmentId);
    }
    if ($payment_status) {
      $this->db_readonly->where('fss.payment_status',$payment_status);
    }
    $feeQuery = $this->db_readonly->get()->result();

    // Fetch component-wise data for the same students only if requested
    $componentData = array();
    if ($show_component_wise) {
      $componentData = $this->get_component_data_for_students($cohortStudentIds, $fee_type, $installmentId);
    }

    $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, c.class_name as className, ifnull(cs.section_name,'') as sectionName, sa.admission_no, p.first_name as father_name,  ifnull(p.mobile_no,'') as father_mobile_no, sa.sts_number, sy.picture_url, sa.gender, sa.admission_acad_year_id, c.id as cId, cs.id as csId, (case when sy.promotion_status = 4 or sy.promotion_status = 5  then sy.promotion_status else sa.admission_status end) as admission_status, ifnull(sem.sem_name,'NA') as semester, ifnull(sy.combination,'') as combination, ifnull(sy.boarding,0) as boarding, ifnull(af.application_no,'NA') as application_no, ifnull(sa.enrollment_number,'NA') as enrollment_number, sa.category, sa.caste")
    ->from('student_admission sa')
    ->where_in('sa.id',$cohortStudentIds)
    ->join('student_year sy','sa.id=sy.student_admission_id')
    ->where('sy.acad_year_id',$this->yearId)
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id','left')
    ->join('admission_forms af','sa.admission_form_id = af.id','left')
    ->join('semester sem','sy.semester=sem.id','left')
    ->join('student_relation sr',"sr.std_id=sa.id and relation_type='Father'")
    ->join('parent p','p.id=sr.relation_id');
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
     if ($acad_year_id) {
      $this->db_readonly->where_in('sa.admission_acad_year_id',$acad_year_id);
    }
     if ($classId) {
      $this->db_readonly->where_in('c.id',$classId);
    }
    if ($classSectionId) {
      $this->db_readonly->where_in('cs.id',$classSectionId);
    }
    if ($admission_type) {
      $this->db_readonly->where('sy.admission_type',$admission_type);
    }
    if ($combination) {
      $this->db_readonly->where_in('sy.combination',$combination);
    }
    if ($mediumId) {
      $this->db_readonly->where('sy.medium',$mediumId);
    }
    if ($category) {
      $this->db_readonly->where('sa.category',$category);
    }
    if ($rte_nrteId) {
      $this->db_readonly->where('sy.is_rte',$rte_nrteId);
    }
    if ($donorsId) {
      if ($donorsId == 1) {
        $this->db_readonly->where('sy.donor is null');
      }else{
        $this->db_readonly->where_in('sy.donor',$donorsId);
      }
    }
    // $this->db_readonly->order_by('c.id, cs.id, sa.first_name');
    $studentQuery = $this->db_readonly->get()->result();
    $prevousYearId = $this->yearId - 1;
    $prevousYearname= $this->acad_year->getAcadYearById($prevousYearId);
   $this->db_readonly->select("fb.name as bpName, (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0)) as balance, fcs.student_id")
    ->from('feev2_blueprint fb')
    ->where('fb.acad_year_id <= ',$prevousYearId)
    ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
    ->where_in('fcs.student_id',$cohortStudentIds)
    ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
    ->where('fss.payment_status!=','FULL');
    // if ($fee_type) {
    //   $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
    // }
    $previousBalance =  $this->db_readonly->get()->result();
    $distrubanceCount = $this->get_fees_distrubance_student_details();
    $prevousNonContinueCount = 0;
    if(!empty($distrubanceCount)){
      $prevousNonContinueCount = $distrubanceCount['prevousNonContinueCount'];
    }

    $additionalAmount = $this->db_readonly->select("fdm.student_id, sum(total_amount- total_used_amount - excess_refund_amount) as addtAmount")
    ->from('feev2_additional_amount fdm')
    ->where_in('fdm.student_id',$cohortStudentIds)
    ->group_by('fdm.student_id')
    ->get()->result();

    // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
    $boardingSettings = $this->settings->getSetting('boarding');
    $categorySettings = $this->settings->getSetting('category');
    $shcIds = [];
    foreach ($feeQuery as $key => $value) {
      $shcIds[] = $value->schid;
    }
    foreach ($feeQuery as $key => &$fee) {
      foreach ($studentQuery as $key => $std) {
        if ($fee->student_id == $std->stdId) {
          $fee->student_name = $std->student_name;
          $fee->class_name = $std->class_name;
          $fee->className = $std->className;
          $fee->semester = $std->semester;
          $fee->sectionName = $std->sectionName;
          $fee->admission_no = $std->admission_no;
          $fee->combination = $std->combination;
          $fee->father_name = $std->father_name;
          $fee->father_mobile_no = $std->father_mobile_no;
          $fee->stdId = $std->stdId;
          $fee->cId = $std->cId;
          $fee->csId = $std->csId;
          $fee->admission_status = $std->admission_status;
          $fee->application_no = $std->application_no;
          $fee->enrollment_number = $std->enrollment_number;
          $fee->boarding_type = isset($boardingSettings[$std->boarding]) && !empty($boardingSettings[$std->boarding]) ? $boardingSettings[$std->boarding] : 'NA';
          $fee->category = isset($categorySettings[$std->category]) && !empty($categorySettings[$std->category]) ? $categorySettings[$std->category] : 'NA';
          $fee->caste = isset($std->caste) && !empty($std->caste) ? $std->caste : 'NA';

          // Add component-wise data to the fee record
          if (!empty($componentData['feeArray']) && isset($componentData['feeArray'][$fee->student_id])) {
            $fee->component_breakdown = $componentData['feeArray'][$fee->student_id];
          } else {
            $fee->component_breakdown = array();
          }
        }
      }
    }

    foreach ($feeQuery as $key => &$fee) {
      $fee->previous_balance = 0;
      if (!empty($previousBalance)) {
        foreach ($previousBalance as $key => $prevBal) {
          if ($fee->student_id == $prevBal->student_id) {
            $fee->previous_balance += $prevBal->balance;
          }
        }
      }
    }
    foreach ($feeQuery as $key => &$fee) {
      $fee->addtional_amount = 0;
       if (!empty($additionalAmount)) {
        foreach ($additionalAmount as $key => $addt) {
          if ($fee->student_id == $addt->student_id) {
            $fee->addtional_amount = $addt->addtAmount;
          }
        }
      }
    }

    array_multisort(array_column($feeQuery, 'cId'), SORT_ASC, array_column($feeQuery, 'csId'), SORT_ASC, array_column($feeQuery, 'student_name'), SORT_ASC, $feeQuery);

    $transcation = $this->db_readonly->select("ft.id as transid, ft.student_id, ft.amount_paid, date_format(ft.paid_datetime,'%d-%m-%Y') as paid_date, ft.concession_amount, ft.adjustment_amount, ft.fine_amount, ftp.payment_type, fcs.blueprint_id, ft.receipt_number, ftp.reconciliation_status, ifnull(ftp.bank_name,'')  as bank_name, ifnull(date_format(ftp.cheque_or_dd_date,'%d-%m-%Y'),'') as cheque_or_dd_date, ifnull(ftp.cheque_dd_nb_cc_dd_number,'') as  cheque_dd_nb_cc_dd_number, ftp.recon_submitted_on, ifnull(ft.loan_provider_charges,0) as loan_provider_charges")
    ->from('feev2_transaction ft')
    ->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
    ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
    ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id and reconciliation_status !=3 and reconciliation_status !=1')
    ->where('ft.soft_delete!=',1)
    ->where_in('ft.fee_student_schedule_id',$shcIds)
    ->where('ft.status','SUCCESS')
    ->order_by('fcs.blueprint_id')
    ->get()->result();

    $this->db_readonly->select('fi.name, fi.id, fb.id as fbId, fi.installment_order as flag, fit.name as installmentsType, fb.name as blueprint_name')
    ->from('feev2_blueprint fb')
    ->where('fb.acad_year_id',$this->yearId)
    ->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id')
    ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
    ->join('feev2_installments fi','fit.id=fi.feev2_installment_type_id')
    ->order_by('fi.id');
    if ($fee_type) {
     $this->db_readonly->where_in('fb.id',$fee_type);
    }
    if ($installmentId) {
     $this->db_readonly->where_in('fi.id',$installmentId);
    }
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    $headersIns = $this->db_readonly->get()->result();

    $bpIds = [];
    foreach ($headersIns as $key => $val) {
      // $bpIds[$val->fbId]['blueprint_name']=$val->blueprint_name;
      if ($val->flag != 0) {
        $bpIds[$val->fbId][$val->flag]['name']=$val->blueprint_name;
        $bpIds[$val->fbId][$val->flag]['id']=$val->id;
        $bpIds[$val->fbId][$val->flag]['installments']=$val->name;
      }else{
        $bpIds[$val->fbId][$val->id]['name']=$val->blueprint_name;
        $bpIds[$val->fbId][$val->id]['id']=$val->id;
        $bpIds[$val->fbId][$val->id]['installments']=$val->name;
      }
    }
    ksort($bpIds);
    $bpHeaderName = [];
    foreach ($headersIns as $key => $val) {
      $bpHeaderName[$val->fbId]['blueprint_name']=$val->blueprint_name;
    }
    $bpArry = [];
    $blueprintsTotal = [];
    $bp_students = [];
    $bp_students_discounts = [];
    $bp_students_loan = [];
    $blueprintsLoan = [];
    $discountsStudentwise = [];

    foreach ($feeQuery as $key => $val) {
      if (!array_key_exists($val->blueprint_id, $blueprintsTotal)) {
        $blueprintsTotal[$val->blueprint_id] = array();
        $blueprintsTotal[$val->blueprint_id]['total_fee_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['total_balance_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['total_fee_paid_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['total_concession_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['applied_concession_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['received_concession_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['applied_adjustment_summary'] = 0;
        $blueprintsTotal[$val->blueprint_id]['applied_fine_summary'] = 0;
        // $blueprintsTotal[$val->blueprint_id]['previous_balance'] = 0;
        // $blueprintsTotal[$val->blueprint_id]['excess_amount'] = 0;
        // $blueprintsTotal[$val->blueprint_id]['applied_discount'] = 0;
        $blueprintsTotal[$val->blueprint_id]['loan_provider_charges'] = 0;
        $bp_students[$val->blueprint_id] = [];
      }
      $bp_students[$val->blueprint_id][] = $val->stdId;
      $bp_students_discounts[$val->blueprint_id][$val->stdId] = $val->discount;
      $blueprintsLoan[$val->blueprint_id][$val->stdId] = $val->loan_provider_charges;
      $discountsStudentwise[$val->stdId][$val->blueprint_id] = $val->discount;

      if(!array_key_exists($val->stdId, $bpArry)) {
        $bpArry[$val->stdId] = array();
        $bpArry[$val->stdId]['total_fee'] = 0;
        $bpArry[$val->stdId]['previous_bal'] = 0;
        $bpArry[$val->stdId]['addt_amount'] = 0;
        $bpArry[$val->stdId]['total_balance'] = 0;
        $bpArry[$val->stdId]['total_concession'] = 0;
        $bpArry[$val->stdId]['total_adjustment'] = 0;
        $bpArry[$val->stdId]['total_fine'] = 0;
        $bpArry[$val->stdId]['total_concession_sch'] = 0;
        $bpArry[$val->stdId]['total_adjustment_sch'] = 0;
        $bpArry[$val->stdId]['total_fine_sch'] = 0;
        $bpArry[$val->stdId]['total_fee_paid'] = 0;
        $bpArry[$val->stdId]['total_discount'] = 0;
        $bpArry[$val->stdId]['total_previous'] = 0;
        $bpArry[$val->stdId]['total_excess'] = 0;
        $bpArry[$val->stdId]['stdId'] = $val->stdId;
        $bpArry[$val->stdId]['key'] = $key;
        $bpArry[$val->stdId]['student_name'] = $val->student_name;
        $bpArry[$val->stdId]['class_name'] = $val->class_name;
        $bpArry[$val->stdId]['className'] = $val->className;
        $bpArry[$val->stdId]['semester'] = $val->semester;
        $bpArry[$val->stdId]['sectionName'] = $val->sectionName;
        $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
        $bpArry[$val->stdId]['combination'] = $val->combination;
        $bpArry[$val->stdId]['parent_name'] = $val->father_name;
        $bpArry[$val->stdId]['mobile_no'] = $val->father_mobile_no;
        // $bpArry[$val->stdId]['discount'] = $val->discount;
        $bpArry[$val->stdId]['fine_amount'] = $val->fine_amount;
        $bpArry[$val->stdId]['card_charge_amount'] = $val->card_charge_amount;
        $bpArry[$val->stdId]['refund_amount'] = $val->refund_amount;
        $bpArry[$val->stdId]['admission_status'] = $val->admission_status;
        $bpArry[$val->stdId]['loan_provider_charges'] = $val->loan_provider_charges;
        $bpArry[$val->stdId]['application_no'] = $val->application_no;
        $bpArry[$val->stdId]['enrollment_number'] = $val->enrollment_number;
        $bpArry[$val->stdId]['boarding_type'] = $val->boarding_type;
        $bpArry[$val->stdId]['category'] = $val->category;
        $bpArry[$val->stdId]['caste'] = $val->caste;
        $bpArry[$val->stdId]['total_due_amount'] =  0;
        if ($val->insFlag !=0) {
          $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insFlag] = array();
        }else{
          $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insId] = array();;
        }
        // $bpArry[$val->stdId]['installments'][$val->blueprint_id][$val->insId] = array();
      }


      if ($val->insFlag !=0) {
        $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insFlag] = array(
          'installment_amount'=> $val->installment_amount,
          'installment_amount_paid'=> $val->installment_amount_paid,
          'status'=> $val->installment_status,
          'concession'=> $val->concession,
          'adjustment'=> $val->adjustment,
          'fine'=> $val->fine_applied,
        );
      }else{
        $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insId] = array(
          'installment_amount'=> $val->installment_amount,
          'installment_amount_paid'=> $val->installment_amount_paid,
          'status'=> $val->installment_status,
          'concession'=> $val->concession,
          'adjustment'=> $val->adjustment,
          'fine'=> $val->fine_applied,
        );
      }


      $bpArry[$val->stdId]['total_fee'] += $val->installment_amount;
      $bpArry[$val->stdId]['total_due_amount'] +=  $val->due_amount;
      $bpArry[$val->stdId]['previous_bal'] = $val->previous_balance;
      $bpArry[$val->stdId]['addt_amount'] = $val->addtional_amount;
      // $bpArry[$val->stdId]['total_previous'] += $val->previous_balance;
      // $bpArry[$val->stdId]['total_excess'] += $val->addtional_amount;
      $bpArry[$val->stdId]['total_balance'] += ($val->installment_amount - $val->installment_amount_paid - $val->concession);
      $bpArry[$val->stdId]['total_concession'] += $val->concession;
      $bpArry[$val->stdId]['total_adjustment'] += $val->adjustment;
      $bpArry[$val->stdId]['total_fine'] += $val->fine_applied;
      // $bpArry[$val->stdId]['discount'] += $val->fine_applied;

      $bpArry[$val->stdId]['total_concession_sch'] = $val->total_concession;
      $bpArry[$val->stdId]['total_adjustment_sch'] = $val->total_adjustment;
      $bpArry[$val->stdId]['total_fine_sch'] = $val->fine_applied;
      $bpArry[$val->stdId]['total_fee_paid'] += $val->installment_amount_paid;
      // $bpArry[$val->stdId]['discount'] = $val->discount;

      $blueprintsTotal[$val->blueprint_id]['total_fee_summary'] +=  $val->installment_amount;
      $blueprintsTotal[$val->blueprint_id]['total_balance_summary'] += ($val->installment_amount - $val->installment_amount_paid - $val->concession);
      $blueprintsTotal[$val->blueprint_id]['total_concession_summary'] += $val->concession;
      $blueprintsTotal[$val->blueprint_id]['total_fee_paid_summary'] += $val->installment_amount_paid;
      $blueprintsTotal[$val->blueprint_id]['applied_concession_summary'] += $val->applied_concession;
      $blueprintsTotal[$val->blueprint_id]['received_concession_summary'] += $val->received_concession;
      // $blueprintsTotal[$val->blueprint_id]['loan_provider_charges'] += $val->loan_provider_charges;
      $blueprintsTotal[$val->blueprint_id]['applied_adjustment_summary'] += $val->adjustment_applied;
      $blueprintsTotal[$val->blueprint_id]['applied_fine_summary'] += $val->fine_applied;
      // $blueprintsTotal[$val->blueprint_id]['previous_balance'] += $val->previous_balance;
      // $blueprintsTotal[$val->blueprint_id]['excess_amount'] += $val->addtional_amount;
      // $blueprintsTotal[$val->blueprint_id]['applied_discount'] = $val->discount;
    }

    foreach ($bp_students as $blueprint_id => $bp_std) {
      $std = array_unique($bp_std);
      $blueprintsTotal[$blueprint_id]['total_assigned'] = count($std);
      // $blueprintsTotal[$blueprint_id]['applied_discount'] = count($std);
    }
    foreach ($blueprintsLoan as $blueprint_id => $loan) {
      $blueprintsTotal[$blueprint_id]['loan_provider_charges'] = array_sum($loan);
    }

    foreach ($bp_students_discounts as $blueprint_id => $dicount) {
      $blueprintsTotal[$blueprint_id]['applied_discount'] = array_sum($dicount);
    }
    $fee_studentArr = [];
    foreach ($bpArry as $stdId => $arr) {
      $arr['discount'] = array_sum($discountsStudentwise[$stdId]);

      // Add component breakdown to each student's data
      if (!empty($componentData['feeArray']) && isset($componentData['feeArray'][$stdId])) {
        $arr['component_breakdown'] = $componentData['feeArray'][$stdId];
      } else {
        $arr['component_breakdown'] = array();
      }

      array_push($fee_studentArr, $arr);
    }

    $transactions = [];
    foreach ($transcation as $key => $tran) {
      // if (!array_key_exists($tran->student_id, $transactions)) {
      //   $transactions[$tran->student_id]['transaction'][$tran->blueprint_id] = array();
      // }
      $transactions[$tran->student_id]['transaction'][$tran->blueprint_id][] = array(
        'receipt_number' => $tran->receipt_number,
        'paid_date' => $tran->paid_date,
        'amount_paid' => $tran->amount_paid,
        'concession_amount' => $tran->concession_amount,
        'adjustment_amount' => $tran->adjustment_amount,
        'fine_amount' => $tran->fine_amount,
        'payment_type' => $tran->payment_type,
        'bank_name' => $tran->bank_name,
        'cheque_or_dd_date' => $tran->cheque_or_dd_date,
        'cheque_dd_nb_cc_dd_number' => $tran->cheque_dd_nb_cc_dd_number,
      );
    }
    // usort($bpArry, function($a, $b) { return $a['key'] - $b['key']; });

    // echo "<pre>"; print_r($bpArry);
    // die();

    $loan_column = $this->settings->getSetting('loan_provider_charges');
    $adjustment = $this->settings->getSetting('fee_adjustment_amount');
    $fineAmount = $this->settings->getSetting('fee_fine_amount');
    $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');
    $combination_column = $this->settings->getSetting('fees_stuent_summary_report_show_combination_filter');

    $header = '<thead style="display: table-header-group;">';
    $header .= '<tr>';
    $header .= '<th rowspan="2">#</th>';
    $header .= '<th rowspan="2">Student</th>';
    $header .= '<th rowspan="2">Class</th>';
    $header .= '<th rowspan="2">Section</th>';
    $header .= '<th rowspan="2">Category</th>';
    $header .= '<th rowspan="2">Caste</th>';
    if ($is_semester_scheme == 1) {
      $header .= '<th rowspan="2">Semester</th>';
    }
    $header .= '<th rowspan="2">Admission No</th>';
    $header .= '<th rowspan="2">Enrollment No</th>';
    $header .= '<th rowspan="2">Boarding Type</th>';
    $header .= '<th rowspan="2">Application No</th>';
    if($combination_column == 1){
      $header .= '<th rowspan="2">Combination</th>';
    }
    $header .= '<th rowspan="2">Parent Name</th>';
    $header .= '<th rowspan="2">Parent Number</th>';

    $header .='<th rowspan="2" style="text-align:center;">Previous Balance</th>';
    $header .='<th rowspan="2" style="text-align:center;">Excess Fee</th>';

    // $header .= '<tr>';

    $colSpan = '7';
    $insColspan = '5';
    $transSpan = '8';

    if($adjustment){
      $colSpan++;
      $insColspan++;
      $transSpan++;
    }

    if($fineAmount){
      $colSpan++;
      $insColspan++;
      $transSpan++;
    }

    if ($fineAmount && $adjustment) {
      $insColspan = 7;
      $transSpan = 10;
    } elseif ($fineAmount || $adjustment) {
      $insColspan = 6;
      $transSpan = 9;
    }

    if ($loan_column) {
      $colSpan++;
      if ($fineAmount && $adjustment) {
        $insColspan = 7;
        $transSpan = 10;
      } elseif ($fineAmount || $adjustment) {
        $insColspan = 6;
        $transSpan = 9;
      }
      // $transSpan = 8;
    }

    $header .='<th colspan="'.$colSpan.'" style="text-align:center;">Over all Total</th>';

    // Check if component-wise display is requested
    if ($show_component_wise && !empty($componentData['component_headers'])) {
      // Display component-wise headers
      foreach ($componentData['component_headers'] as $componentKey => $componentName) {
        $header .='<th colspan="4" style="text-align:center" >'.$componentName.'</th>';
      }
    } else {
      // Display regular installment headers
      foreach ($bpIds as $bpId => $val) {
        foreach ($val as $ins_id => $v) {
          $insName = $v['installments'].' - '.$v['name'];
          $header .='<th colspan="'.$insColspan.'" style="text-align:center" >'.$insName.'</th>';
        }
        if (!$transaction_hide_show) {
          $header .= '<th colspan="'.$transSpan.'" style="text-align:center;vertical-align:middle">Transactions</th>';
        }
      }
    }
    $header .= '</tr>';

    $header .= '<tr>';
    $header .= '<th>Fee Amount</th>';
    $header .= '<th>Fee Paid</th>';
    $header .= '<th>Total Concession</th>';
    if ($adjustment) {
      $header .= '<th>Total Adjustment</th>';
    }
    if ($fineAmount) {
      $header .= '<th>Total Fine (Collected)</th>';
    }
    if ($loan_column) {
      $header .= '<th>Loan Provider Charges</th>';
    }
    $header .= '<th>Discount</th>';
    $header .= '<th>Balance</th>';
    $header .= '<th>Percentage (%)</th>';
    $header .= '<th>Overdue Balance</th>';

    // Check if component-wise display is requested
    if ($show_component_wise && !empty($componentData['component_headers'])) {
      // Display component-wise sub-headers
      foreach ($componentData['component_headers'] as $componentKey => $componentName) {
        $header .= '<th>Total Amount</th>';
        $header .= '<th>Amount Paid</th>';
        $header .= '<th>Concession</th>';
        $header .= '<th>Balance</th>';
      }
    } else {
      // Display regular installment sub-headers
      foreach ($bpIds as $bpId => $val) {
        foreach ($val as $ins_id => $ins_name) {
          $header .= '<th>Fee Amount</th>';
          $header .= '<th>Collected</th>';
          $header .= '<th>Concession</th>';
          if ($adjustment) {
            $header .= '<th>Adjustment</th>';
          }
          if ($fineAmount) {
            $header .= '<th>Fine</th>';
          }
          $header .= '<th>Balance</th>';
          $header .= '<th>Status</th>';
        }
        if (!$transaction_hide_show) {
          $header .= '<th>Paid Date</th>';
          $header .= '<th>Receipt Number</th>';
          $header .= '<th>Amount Paid</th>';
          $header .= '<th>Concession</th>';
          if ($adjustment) {
            $header .= '<th>Adjustment</th>';
          }
          if ($fineAmount) {
            $header .= '<th>Fine</th>';
          }
          $header .= '<th>Payment Mode</th>';
          $header .= '<th>Bank Name</th>';
          $header .= '<th>DD/Cheque Date</th>';
          $header .= '<th>Reference No.</th>';
        }
      }
    }
    $header .= '</tr>';

    $header .= '</thead>';
    // echo "<pre>"; print_r($fee_studentArr);
    // Add component headers to the return data
    $componentHeaders = !empty($componentData['component_headers']) ? $componentData['component_headers'] : array();

    // Create component-wise data structure for dual display
    $componentWiseData = $this->build_component_wise_display_data($fee_studentArr, $componentData, $componentHeaders);

    return array(
      'fee_data' => $fee_studentArr,
      'header' => $header,
      'headers'=>$bpIds,
      'summary'=>$blueprintsTotal,
      'transactions'=>$transactions,
      'summaryHeaderbp'=>$bpHeaderName,
      'prevousYearname'=>$prevousYearname,
      'prevousNonContinueCount'=>$prevousNonContinueCount,
      'component_headers' => $componentHeaders,
      'has_component_data' => !empty($componentHeaders),
      'component_wise_data' => $componentWiseData,
      'dual_display_ready' => true,
      'show_component_wise' => $show_component_wise
    );
  }

  /**
   * Utility function to merge regular fee data with component-wise data
   * @param array $regular_data - Regular fee data from get_fee_summary_student_wise_v2_details_new
   * @param array $component_data - Component data from get_student_wise_component_getStudentsForSummary_data
   * @return array - Merged data structure
   */
  public function merge_regular_and_component_data($regular_data, $component_data) {
    $merged_data = $regular_data;

    // Add component information to each student's fee data
    if (!empty($component_data['feeArray'])) {
      foreach ($merged_data['fee_data'] as &$student_fee) {
        $student_id = $student_fee['stdId'];

        // Add component breakdown if available for this student
        if (isset($component_data['feeArray'][$student_id])) {
          $student_fee['component_breakdown'] = $component_data['feeArray'][$student_id];
        }
      }
    }

    // Add component headers for table construction
    if (!empty($component_data['component_headers'])) {
      $merged_data['component_headers'] = $component_data['component_headers'];
    }

    // Add component student data
    if (!empty($component_data['student_data'])) {
      $merged_data['component_student_data'] = $component_data['student_data'];
    }

    $merged_data['has_component_data'] = true;

    return $merged_data;
  }

  /**
   * Get component-wise data for specific students and fee types
   * This is a simplified version that can be called from the regular function
   */
  public function get_component_data_for_students($student_ids, $fee_type, $installmentId = null) {
    if (empty($student_ids)) {
      return array();
    }

    $this->db_readonly->select('
      fi.name as installment_name,
      fbc.name as component_name,
      fcs.student_id,
      fsic.blueprint_component_id as comp_id,
      sum(fsic.component_amount) as component_amount,
      sum(ifnull(fsic.component_amount_paid,0)) as component_amount_paid,
      sum(ifnull(fsic.concession_amount,0) + ifnull(fsic.concession_amount_paid,0)) as concession_amount,
      sum(ifnull(fsi.total_fine_amount,0)) as fine_amount,
      (sum(fsic.component_amount) - sum(ifnull(fsic.component_amount_paid,0)) - sum(ifnull(fsic.concession_amount,0) + ifnull(fsic.concession_amount_paid,0))) as balance_amount
    ')
    ->from('feev2_cohort_student fcs')
    ->join('feev2_student_schedule fss','fss.feev2_cohort_student_id=fcs.id')
    ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
    ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
    ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
    ->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id')
    ->join("feev2_installments fi","fsi.feev2_installments_id=fi.id")
    ->where('fb.acad_year_id',$this->yearId)
    ->where_in('fcs.student_id',$student_ids)
    ->group_by('fcs.student_id, fsic.blueprint_component_id, fi.id')
    ->order_by('fcs.student_id, fi.id, fbc.name');

    if ($fee_type) {
      $this->db_readonly->where_in('fb.id',$fee_type);
    }
    if ($installmentId) {
      $this->db_readonly->where('fsi.feev2_installments_id',$installmentId);
    }

    $component_data = $this->db_readonly->get()->result();

    // Organize data by student and component
    $feeArray = [];
    $componentHeaders = [];
    foreach ($component_data as $val) {
      $componentKey = $val->installment_name . ' - ' . $val->component_name;
      $componentHeaders[$componentKey] = $componentKey;
      $feeArray[$val->student_id][$componentKey] = $val;
    }

    return array(
      'component_headers' => $componentHeaders,
      'feeArray' => $feeArray
    );
  }

  /**
   * Build component-wise display data from regular fee data
   * This creates a structure that can display both regular and component views
   */
  public function build_component_wise_display_data($fee_studentArr, $componentData, $componentHeaders) {
    $componentWiseData = array();

    if (empty($componentHeaders) || empty($componentData['feeArray'])) {
      return $componentWiseData;
    }

    foreach ($fee_studentArr as $student) {
      $studentId = $student['stdId'];

      if (!isset($componentData['feeArray'][$studentId])) {
        continue;
      }

      $studentComponentData = $componentData['feeArray'][$studentId];

      // Create component-wise rows for this student
      foreach ($componentHeaders as $componentKey => $componentName) {
        if (isset($studentComponentData[$componentKey])) {
          $component = $studentComponentData[$componentKey];

          $componentRow = array(
            'stdId' => $studentId,
            'student_name' => $student['student_name'],
            'class_name' => $student['class_name'],
            'className' => $student['className'],
            'sectionName' => $student['sectionName'],
            'admission_no' => $student['admission_no'],
            'enrollment_number' => isset($student['enrollment_number']) ? $student['enrollment_number'] : 'N/A',
            'parent_name' => $student['parent_name'],
            'mobile_no' => $student['mobile_no'],
            'category' => isset($student['category']) ? $student['category'] : 'N/A',
            'caste' => isset($student['caste']) ? $student['caste'] : 'N/A',
            'boarding_type' => isset($student['boarding_type']) ? $student['boarding_type'] : 'N/A',
            'application_no' => isset($student['application_no']) ? $student['application_no'] : 'N/A',
            'combination' => isset($student['combination']) ? $student['combination'] : '',
            'semester' => isset($student['semester']) ? $student['semester'] : 'N/A',
            'admission_status' => isset($student['admission_status']) ? $student['admission_status'] : '2',

            // Component-specific data
            'component_name' => $componentName,
            'component_key' => $componentKey,
            'installment_name' => isset($component->installment_name) ? $component->installment_name : 'N/A',
            'component_amount' => isset($component->component_amount) ? $component->component_amount : 0,
            'component_amount_paid' => isset($component->component_amount_paid) ? $component->component_amount_paid : 0,
            'concession_amount' => isset($component->concession_amount) ? $component->concession_amount : 0,
            'fine_amount' => isset($component->fine_amount) ? $component->fine_amount : 0,
            'balance_amount' => isset($component->balance_amount) ? $component->balance_amount : 0,

            // Calculated fields
            'total_component_fee' => isset($component->component_amount) ? $component->component_amount : 0,
            'total_component_paid' => isset($component->component_amount_paid) ? $component->component_amount_paid : 0,
            'total_component_balance' => isset($component->balance_amount) ? $component->balance_amount : 0,

            // Display type
            'display_type' => 'component_wise',
            'row_type' => 'component'
          );

          $componentWiseData[] = $componentRow;
        }
      }
    }

    return $componentWiseData;
  }

    public function get_fee_student_list_for_deactivations($classSectionId, $installmentId, $fee_type, $student_name, $fees_status,$rte_status,$admission_type){
      $name = strtolower($student_name);

      $this->db_readonly->select("if(sy.is_rte = 1, 'RTE', 'NON-RTE') as rte_status,if(sy.admission_type= 1, 'Re-Admission', 'New Admission') as admission_type,sd.id as stdId, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, cs.class_name as className, cs.section_name as sectionName, sd.admission_no, sd.temp_deactivation, 'Not Assigned' as assignStatus, ifnull(sd.enrollment_number,'NA') as enrollment_number")
        ->from('student_admission sd')
        // ->where_in('sd.id',array('661'))
        ->join('student_year sy','sd.id=sy.student_admission_id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs',"sy.class_section_id=cs.id and cs.is_placeholder!=1")
        ->where('sd.admission_status', 2)
        ->where('sy.promotion_status!=', 4)
        ->where('sy.promotion_status!=', 5)
        ->order_by('c.id, cs.id, sd.first_name');
        if ($classSectionId) {
          $this->db_readonly->where_in('cs.id',$classSectionId);
        }
        if ($rte_status) {
          $this->db_readonly->where_in('sy.is_rte',$rte_status);
        }
        if ($admission_type) {
          $this->db_readonly->where_in('sy.admission_type',$admission_type);
        }
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($student_name) {
          $this->db_readonly->where("(LOWER(sd.first_name) like '%$name%' OR (LOWER(sd.last_name) like '%$name%'))");
        }
        $this->db_readonly->order_by('c.id','cs.id','sa.first_name');
        $stdQuery = $this->db_readonly->get()->result();
        if(empty($stdQuery)){
          return 0;
        }
        $studentIds = [];
        foreach ($stdQuery as $key => $val) {
          array_push($studentIds, $val->stdId);
        }

        $this->db_readonly->select("fcs.blueprint_id, if(fi.end_date<CURDATE(), 'Is Due', fsi.status) as due_status, fcs.student_id, 'Assigned' as assignStatus, fsi.status as fee_status, fsi.feev2_installments_id");
        $this->db_readonly->from('feev2_cohort_student fcs');
        if ($fee_type != 'all') {
          $this->db_readonly->where('fcs.blueprint_id',$fee_type);
        }
        $this->db_readonly->group_start();
        $stdIDsChunk = array_chunk($studentIds,200);
        foreach($stdIDsChunk as $std)
        {
          $this->db_readonly->or_where_in('fcs.student_id', $std);
        }
        $this->db_readonly->group_end();
        $this->db_readonly->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
        $this->db_readonly->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id');
        $this->db_readonly->join('feev2_installments fi','fsi.feev2_installments_id=fi.id');
        if ($fees_status) {
          $this->db_readonly->where('fsi.status',$fees_status);
        }
        if ($installmentId) {
          $this->db_readonly->where('fsi.feev2_installments_id',$installmentId);
        }
        $feeStds = $this->db_readonly->get()->result();

        if($fee_type == 'all') {
        $this->db_readonly->select('fb.id, fb.name');
        $this->db_readonly->where('fb.acad_year_id',$this->yearId);
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $headers = $this->db_readonly->get('feev2_blueprint fb')->result();

        foreach ($stdQuery as $key => &$std) {
          $std->feeDetails = [];
          foreach ($feeStds as $key => $fee) {
            if ($std->stdId == $fee->student_id) {
              $std->feeDetails[] = array(
                'blueprint_id'=>$fee->blueprint_id,
                'fee_status'=>$fee->fee_status,
                'due_status'=>$fee->due_status,
                'assignStatus'=>$fee->assignStatus,
              );
            }
          }
        }
        $bpArry = [];
        foreach ($stdQuery as $key => $val) {
          if(!array_key_exists($val->stdId, $bpArry)) {
            $bpArry[$val->stdId] = array();
            $bpArry[$val->stdId]['stdId'] = $val->stdId;
            $bpArry[$val->stdId]['student_name'] = $val->student_name;
            $bpArry[$val->stdId]['class_name'] = $val->class_name;
            $bpArry[$val->stdId]['className'] = $val->className;
            $bpArry[$val->stdId]['sectionName'] = $val->sectionName;
            $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
            $bpArry[$val->stdId]['enrollment_number'] = $val->enrollment_number;
            $bpArry[$val->stdId]['rte_status'] = $val->rte_status;
            $bpArry[$val->stdId]['admission_type'] = $val->admission_type;
            $bpArry[$val->stdId]['temp_deactivation'] = $val->temp_deactivation;
            foreach ($val->feeDetails as $key => $value) {
              $bpArry[$val->stdId][$value['blueprint_id']] = 'Not Assigned';
            }
          }
          foreach ($val->feeDetails as $key => $value) {
            if($value['assignStatus'] == 'Assigned') {
              $bpArry[$val->stdId][$value['blueprint_id']] = ($value['fee_status'] != 'FULL')?$value['due_status']:$value['fee_status'];
            }
          }
        }
        $header = '<thead>';
        $header .= '<tr>';
        $header .= '<th class="print_hide"></th>';
        $header .= '<th>#</th>';
        $header .= '<th>Status</th>';
        $header .= '<th>Student</th>';
        $header .= '<th>Class</th>';
        $header .= '<th>Section</th>';
        $header .= '<th>Admission No.</th>';
        $header .= '<th>Enrollment No.</th>';
        $header .= '<th>RTE/NON-RTE</th>';
        $header .= '<th>Admission Type</th>';
        foreach ($headers as $key => $val) {
          $header .= '<th>'.$val->name.'</th>';
        }
        $header .= '<th class="print_hide">Action</th>';
        $header .= '</tr>';
        $header .= '</thead>';
      }
      else {
        foreach ($stdQuery as $key => &$std) {
          $std->feeDetails = [];
          foreach ($feeStds as $key => $fee) {
            if ($std->stdId == $fee->student_id) {
              $std->feeDetails[] = array(
                'feev2_installments_id'=>$fee->feev2_installments_id,
                'fee_status'=>$fee->fee_status,
                'due_status'=>$fee->due_status,
                'assignStatus'=>$fee->assignStatus,
              );
            }
          }
        }

        $this->db_readonly->select('fi.name, fi.id')
        ->from('feev2_blueprint fb')
        ->where('fb.id',$fee_type)
        ->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id')
        ->join('feev2_installments fi','fbit.feev2_installment_type_id=fi.feev2_installment_type_id')
        ->order_by('fi.id');
        if ($installmentId) {
         $this->db_readonly->where('fi.id',$installmentId);
        }
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $headers = $this->db_readonly->get()->result();

        // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
        $this->db_readonly->select('fb.id, fb.name');
        $this->db_readonly->where('fb.id',$fee_type);
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $bp = $this->db_readonly->get('feev2_blueprint fb')->row();

        $bpArry = [];
        foreach ($stdQuery as $key => $val) {
          if ($fees_status) {
            if (!empty($val->feeDetails)) {
              if(!array_key_exists($val->stdId, $bpArry)) {
                $bpArry[$val->stdId] = array();
                $bpArry[$val->stdId]['stdId'] = $val->stdId;
                $bpArry[$val->stdId]['student_name'] = $val->student_name;
                $bpArry[$val->stdId]['className'] = $val->className;
                $bpArry[$val->stdId]['sectionName'] = $val->sectionName;
                $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
                $bpArry[$val->stdId]['enrollment_number'] = $val->enrollment_number;
                $bpArry[$val->stdId]['rte_status'] = $val->rte_status;
                $bpArry[$val->stdId]['admission_type'] = $val->admission_type;
                $bpArry[$val->stdId]['temp_deactivation'] = $val->temp_deactivation;
                foreach ($val->feeDetails as $key => $value) {
                  $bpArry[$val->stdId][$value['feev2_installments_id']] = 'Not Assigned';
                }
              }
            }
          }else{
            if(!array_key_exists($val->stdId, $bpArry)) {
              $bpArry[$val->stdId] = array();
              $bpArry[$val->stdId]['stdId'] = $val->stdId;
              $bpArry[$val->stdId]['student_name'] = $val->student_name;
              $bpArry[$val->stdId]['className'] = $val->className;
              $bpArry[$val->stdId]['sectionName'] = $val->sectionName;
              $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
              $bpArry[$val->stdId]['enrollment_number'] = $val->enrollment_number;
              $bpArry[$val->stdId]['rte_status'] = $val->rte_status;
              $bpArry[$val->stdId]['admission_type'] = $val->admission_type;
              $bpArry[$val->stdId]['temp_deactivation'] = $val->temp_deactivation;
              foreach ($val->feeDetails as $key => $value) {
                $bpArry[$val->stdId][$value['feev2_installments_id']] = 'Not Assigned';
              }
            }
          }
          foreach ($val->feeDetails as $key => $value) {
            if($value['assignStatus'] == 'Assigned') {
              $bpArry[$val->stdId][$value['feev2_installments_id']] = ($value['fee_status'] != 'FULL')?$value['due_status']:$value['fee_status'];
            }
          }
        }

        $header = '<thead>';
        $header .= '<tr>';
        $header .= '<th class="print_hide" rowspan="2"></th>';
        $header .= '<th rowspan="2">#</th>';
        $header .= '<th rowspan="2">Status</th>';
        $header .= '<th rowspan="2">Student</th>';
        $header .= '<th rowspan="2">Class</th>';
        $header .= '<th rowspan="2">Section</th>';
        $header .= '<th rowspan="2">Admission No.</th>';
        $header .= '<th rowspan="2">Enrollment No.</th>';
        $header .= '<th rowspan="2">RTE/NON-RTE</th>';
        $header .= '<th rowspan="2">Admission Type</th>';
        $header .= '<th colspan="'.count($headers).'">'.$bp->name.'</th>';
        $header .= '<th class="print_hide" rowspan="2">Action</th>';
        $header .= '</tr>';
        foreach ($headers as $key => $val) {
          if($key == 0) $header .= '<tr>';
          $header .= '<th>'.$val->name.'</th>';
        }

        $header .= '</tr>';
        $header .= '</thead>';
      }

      $students = [];
      foreach ($bpArry as $key => $value) {
        $students[] = $value;
      }
      // echo '<pre>'; print_r($this->db_readonly->last_query()); die();
      return array('headers' => $headers, 'students' => $students, 'header' => $header);
    }

    public function student_temp_activation($status, $student_ids) {
      if(empty($student_ids)) return 1;
      return $this->db->where_in('id', $student_ids)->update('student_admission', ['temp_deactivation' => $status]);
    }

    public function fee_history_data_by_stdbyId($stdId){
      $stdDetails = $this->db_readonly->select("concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, sd.admission_no")
      ->from('student_admission sd')
      ->where('sd.id',$stdId)
      ->join('student_year sy',"sd.id=sy.student_admission_id")
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->get()->row();

      $feeDetails = $this->db_readonly->select("fb.name as blueprint_name, fss.total_fee as fee_amount,  ifnull(fss.total_fee_paid,0) as paid_amount, (ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)) as concession, (ifnull(fss.total_adjustment_amount,0)  + ifnull(fss.total_adjustment_amount_paid,0)) as adjustment, (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0)) as balance, fss.id as schId")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$stdId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      // ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId)
      ->get()->result();

      $installments = $this->db_readonly->select("fi.name as insName, ifnull(fsi.installment_amount,0) as installment_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, ifnull(fsi.total_concession_amount,0) as total_concession_amount, ifnull(fsi.total_concession_amount_paid,0) total_concession_amount_paid, (ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0)) as concession, (ifnull(fsi.installment_amount,0) - ifnull(fsi.installment_amount_paid,0) -  ifnull(fsi.total_concession_amount,0)  - ifnull(fsi.total_concession_amount_paid,0)) as balance, fsi.fee_student_schedule_id, if(fi.end_date<CURDATE(), 'Is Due', fsi.status) as due_status, ifnull(fsi.total_adjustment_amount,0) as total_adjustment_amount, ifnull(fsi.total_adjustment_amount_paid,0) total_adjustment_amount_paid, (ifnull(fsi.total_adjustment_amount,0)  + ifnull(fsi.total_adjustment_amount_paid,0)) as adjustment")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$stdId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId)
      ->get()->result();
      $insArry =[];
      foreach ($installments as $key => $ins) {
        $insArry[$ins->fee_student_schedule_id][] = $ins;
      }
      return array('student'=>$stdDetails, 'fees'=>$feeDetails,'installments'=>$insArry);
      // echo "<pre>"; print_r($result); die();
    }

    public function get_installments_detailsbyShcId($schId){

      return $this->db_readonly->select('fi.name as insName, ifnull(fsi.installment_amount,0) as installment_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, ifnull(fsi.total_concession_amount,0) as total_concession_amount, ifnull(fsi.total_concession_amount_paid,0) total_concession_amount_paid, (ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0)) as concession, (ifnull(fsi.installment_amount,0) - ifnull(fsi.installment_amount_paid,0) -  ifnull(fsi.total_concession_amount,0)  - ifnull(fsi.total_concession_amount_paid,0)) as balance')
      ->from('feev2_student_installments fsi')
      ->where('fsi.fee_student_schedule_id',$schId)
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->get()->result();
    }

    public function get_print_receiptbynumber($receiptNumber){
      if (empty($receiptNumber)) {
        return 2;
      }
      $trans = $this->db_readonly->select("ft.id, ft.amount_paid as total_amount,ftp.payment_type, reconciliation_status, cheque_dd_nb_cc_dd_number, ft.receipt_number, date_format(paid_datetime, '%d-%m-%Y') as paid_date, ft.receipt_pdf_link, ft.pdf_status, ft.status, fine_amount, discount_amount, card_charge_amount, concession_amount as trans_concession, ft.refund_amount, ft.concession_amount, concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, ftp.reconciliation_status, ftp.bank_name, date_format(ftp.cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date, ftp.cheque_dd_nb_cc_dd_number, ftp.recon_submitted_on")
      ->from('feev2_transaction ft')
      ->where('ft.receipt_number',$receiptNumber)
      ->where('soft_delete!=',1)
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->join('student_admission sa','ft.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id")
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->get()->row();
      if (!empty($trans)) {
        return $trans;
      }else{
        return 2;
      }
    }

    public function get_balance_sms_student_count($classSectionId, $installmentId, $fee_type, $installment_type, $staff_kid, $classId, $rte_nrteId, $payment_status, $donorsId, $combination, $show_over_due, $student_id, $admissionStatus){

      $this->db_readonly->select('fcs.student_id')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId)
      ->where('fss.payment_status!=','FULL')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id');
      if ($payment_status) {
        $this->db_readonly->where_in('fss.payment_status',$payment_status);
      }
      if($student_id != 0){
        $this->db_readonly->where('fcs.student_id',$student_id);
      }
      if ($fee_type !='all') {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      if ($installmentId) {
        $this->db_readonly->where_in('fsi.feev2_installments_id',$installmentId);
        $this->db_readonly->where('fsi.status!=','FULL');
      }
      $this->db_readonly->group_by('fcs.student_id');
      $cohortStds =  $this->db_readonly->get()->result();

      $studentIds = [];
      foreach ($cohortStds as $key => $val) {
        array_push($studentIds, $val->student_id);
      }
      if(empty($studentIds)){
        return $studentIds;
      }
      $this->db_readonly->select("sa.id as stdId, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status");
      $this->db_readonly->from('student_admission sa');
      $this->db_readonly->group_start();
      $stdIDsChunk = array_chunk($studentIds,200);
      foreach($stdIDsChunk as $std)
      {
        $this->db_readonly->or_where_in('sa.id', $std);
      }
      $this->db_readonly->group_end();
      $this->db_readonly->join('student_year sy','sa.id=sy.student_admission_id');
      $this->db_readonly->where('sy.acad_year_id',$this->yearId);
      $this->db_readonly->join('class c',"sy.class_id=c.id and c.is_placeholder!=1");
      $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
      $this->db_readonly->order_by('c.id, cs.id, sa.first_name');
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($combination) {
        $this->db_readonly->where('sy.combination',$combination);
      }
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($staff_kid) {
        $this->db_readonly->where('sa.has_staff',$staff_kid);
      }
      if ($rte_nrteId) {
        $this->db_readonly->where('sy.is_rte',$rte_nrteId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      if ($donorsId) {
        if ($donorsId == 1) {
          $this->db_readonly->where('sy.donor is null');
        }else{
          $this->db_readonly->where('sy.donor',$donorsId);
        }
      }
      $result = $this->db_readonly->get()->result();

      $stdIds = [];
      if ($admissionStatus) {
        foreach ($result as $key => $res) {
          if (in_array($res->admission_status,$admissionStatus)) {
            array_push($stdIds, $res->stdId);
          }
        }
      } else {
        foreach ($result as $key => $res) {
          array_push($stdIds, $res->stdId);
        }
      }
      return $stdIds;
    }

    public function get_fee_balance_student_list($student_ids, $classSectionId, $installmentId, $fee_type, $installment_type, $staff_kid, $classId, $payment_status,$donorsId, $show_over_due){
        $this->db_readonly->select('fi.name, fi.id, fb.id as bpId, fi.installment_order as flag, fit.name as installmentsType')
        ->from('feev2_blueprint fb')
        ->where('fb.acad_year_id',$this->yearId)
        ->join('feev2_blueprint_installment_types fbit','fb.id=fbit.feev2_blueprint_id')
        ->join('feev2_installment_types fit','fbit.feev2_installment_type_id=fit.id')
        ->join('feev2_installments fi','fit.id=fi.feev2_installment_type_id')
        ->order_by('fi.id');
        if ($fee_type !='all') {
          $this->db_readonly->where_in('fb.id',$fee_type);
        }
        if ($show_over_due) {
          $today = date('Y-m-d');
          $this->db_readonly->where('date_format(fi.end_date,"%Y-%m-%d") < ', $today);
        }
        if ($installmentId) {
         $this->db_readonly->where_in('fi.id',$installmentId);
        }
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $headers = $this->db_readonly->get()->result();

        $headerFlag=array();
        $bpIds = [];
        foreach($headers as $hflag){
          array_push($bpIds, $hflag->bpId);
          if ($hflag->flag != 0) {
            $headerFlag[$hflag->bpId][$hflag->flag]['name']=$hflag->name;
            $headerFlag[$hflag->bpId][$hflag->flag]['id']=$hflag->flag;
            $headerFlag[$hflag->bpId][$hflag->flag]['bpId']=$hflag->bpId;
          }else{
            $headerFlag[$hflag->bpId][$hflag->id]['name']=$hflag->name;
            $headerFlag[$hflag->bpId][$hflag->id]['id']=$hflag->id;
            $headerFlag[$hflag->bpId][$hflag->id]['bpId']=$hflag->bpId;
          }
        }

        $this->db_readonly->select('fb.id, fb.name');
        if ($fee_type  !='all') {
          $this->db_readonly->where_in('fb.id',$fee_type);
        }
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $this->db_readonly->where_in('fb.id',$bpIds);
        $this->db_readonly->where('fb.acad_year_id',$this->yearId);
        $bp = $this->db_readonly->get('feev2_blueprint fb')->result();

        $this->db_readonly->select("sd.id as stdId,  fcs.blueprint_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as class_name,  sd.admission_no, fsi.status as fee_status, fsi.feev2_installments_id, if(fi.end_date<CURDATE(), 'Is Due', fsi.status) as due_status, fsi.installment_amount, fsi.installment_amount_paid, ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0) as concession, ifnull(fsi.total_adjustment_amount,0)  + ifnull(fsi.total_adjustment_amount_paid,0) as adjustment, fss.id as schId,fi.installment_order as insFlag,  ifnull(p1.first_name,'') as father_name, ifnull(p2.first_name,'') as mother_name, ifnull(p1.mobile_no,'')  as f_number, ifnull(p2.mobile_no,'') as m_number, (case when sy.promotion_status = 4 or sy.promotion_status = 5  then sy.promotion_status else sd.admission_status end) as admission_status, fb.name as fee_type, ifnull(sem_name,'NA') as semester, ifnull(sy.combination,' ') as combination, ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0) as total_installment_fine, ifnull(sd.enrollment_number,'NA') as enrollment_number, ifnull(sd.student_mobile_no,'') as student_mobile_no, sd.category, sd.caste")
        ->from('student_admission sd')
        ->where_in('sd.id',$student_ids)
        ->join('student_year sy','sd.id=sy.student_admission_id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('feev2_cohort_student fcs',"sd.id=fcs.student_id")
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->where('fb.acad_year_id',$this->yearId)
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
        ->where('fss.payment_status!=','FULL')
        // ->where('sd.admission_status', 2)
        // ->where('sy.promotion_status!=', 4)
        // ->where('sy.promotion_status!=', 5)
        //
        ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
        ->join('parent p1', 'p1.id=sr1.relation_id')
        ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
        ->join('parent p2', 'p2.id=sr2.relation_id')

        ->join('class c',"sy.class_id=c.id and c.is_placeholder!=1")
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('semester sem','sy.semester=sem.id','left');
        if ($payment_status) {
          $this->db_readonly->where_in('fss.payment_status',$payment_status);
        }
        if ($fee_type !='all') {
          $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
        }
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($classId) {
          $this->db_readonly->where_in('c.id',$classId);
        }
        if ($classSectionId) {
          $this->db_readonly->where_in('cs.id',$classSectionId);
        }
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($staff_kid) {
          $this->db_readonly->where('sd.has_staff',$staff_kid);
        }
        if ($donorsId) {
          if ($donorsId == 1) {
            $this->db_readonly->where('sy.donor is null');
          }else{
            $this->db_readonly->where('sy.donor',$donorsId);
          }
        }
        if ($installmentId) {
          $this->db_readonly->where_in('fsi.feev2_installments_id',$installmentId);
        }
        $this->db_readonly->join('feev2_installments fi','fsi.feev2_installments_id=fi.id');
        if ($show_over_due) {
          $today = date('Y-m-d');
          $this->db_readonly->where('date_format(fi.end_date,"%Y-%m-%d") < ', $today);
        }
        $this->db_readonly->order_by('c.id, cs.id, sd.first_name');
        $result = $this->db_readonly->get()->result();


        $bpArry = [];
        $bpSummaryBalance = [];
        $bp_students = [];
        foreach ($result as $key => $val) {
          if (!array_key_exists($val->blueprint_id, $bpSummaryBalance)) {
            $bpSummaryBalance[$val->blueprint_id] = array();
            $bpSummaryBalance[$val->blueprint_id]['total_balance_summary'] = 0;

            $bp_students[$val->blueprint_id]= [];
          }
          $bp_students[$val->blueprint_id][]  = $val->stdId;
          if(!array_key_exists($val->stdId, $bpArry)) {
            $bpArry[$val->stdId] = array();
            $bpArry[$val->stdId]['stdId'] = $val->stdId;
            $bpArry[$val->stdId]['student_name'] = $val->student_name;
            $bpArry[$val->stdId]['class_name'] = $val->class_name;
            $bpArry[$val->stdId]['semester'] = $val->semester;
            $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
            $bpArry[$val->stdId]['combination'] = $val->combination;
            $bpArry[$val->stdId]['father_name'] = $val->father_name;
            $bpArry[$val->stdId]['mother_name'] = $val->mother_name;
            $bpArry[$val->stdId]['f_number'] = $val->f_number;
            $bpArry[$val->stdId]['m_number'] = $val->m_number;
            $bpArry[$val->stdId]['student_mobile_no'] = $val->student_mobile_no;
            $bpArry[$val->stdId]['admission_status'] = $val->admission_status;
            $bpArry[$val->stdId]['enrollment_number'] = $val->enrollment_number;
            $bpArry[$val->stdId]['fee_type'] = $val->fee_type;
            $bpArry[$val->stdId]['caste'] = (!empty($val->caste)) ? $val->caste : 'NA';
            $bpArry[$val->stdId]['total_balance'] = 0;
            $bpArry[$val->stdId]['total_amount'] = 0;
            $bpArry[$val->stdId]['total_fine'] = 0;
            $bpArry[$val->stdId]['category'] = (!empty($this->settings->getSetting('category')[$val->category])) ? $this->settings->getSetting('category')[$val->category] : 'NA';
            if ($val->insFlag !=0) {
              $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insFlag] = 0;
            }else{
              $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->feev2_installments_id] = '0';
            }

            $bpArry[$val->stdId]['bpId_summary'][$val->blueprint_id] = 0;
          }
          $bpSummaryBalance[$val->blueprint_id]['total_balance_summary'] += $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;
          $bpArry[$val->stdId]['total_balance'] += $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;
          $bpArry[$val->stdId]['total_amount'] += $val->installment_amount;
          $bpArry[$val->stdId]['total_fine'] += $val->total_installment_fine;
          $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insFlag] = 0;
          if ($val->insFlag !=0) {
            $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->insFlag] += $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;;
          }else{
            $bpArry[$val->stdId]['bpId'][$val->blueprint_id][$val->feev2_installments_id] = $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;
          }

          $bpArry[$val->stdId]['bpId_summary'][$val->blueprint_id] = $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;

        }

        $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');
        $combination_column = $this->settings->getSetting('puc_combination');

        foreach ($bp_students as $blueprint_id  => $bp_std) {
          $std = array_unique($bp_std);
          $bpSummaryBalance[$blueprint_id]['total_balance_student'] = count($std);
        }
        $header = '<thead>';
        $header .= '<tr>';
        $header .= '<th class="print_hide" rowspan="2"><input type="checkbox" style="text-align: right" name="selectAll" onclick="check_all(this)" id="selectAll" class="check"></th>';
        $header .= '<th rowspan="2">#</th>';
        $header .= '<th rowspan="2">Student</th>';
        $header .= '<th rowspan="2">Class</th>';
        $header .= '<th rowspan="2">Admission No.</th>';
        $header .= '<th rowspan="2">Enrollment No.</th>';
        $header .= '<th rowspan="2">Student Category</th>';
        $header .= '<th rowspan="2">Student Caste</th>';
        $header .= '<th rowspan="2">Student Mobile No</th>';
        if ($is_semester_scheme == 1) {
          $header .= '<th rowspan="2">Semester</th>';
        }
        if($combination_column == 1){
          $header .= '<th rowspan="2">Combination</th>';
        }
        $header .= '<th rowspan="2">Father</th>';
        $header .= '<th rowspan="2">Father No.</th>';
        $header .= '<th rowspan="2">Mother</th>';
        $header .= '<th rowspan="2">Mother No</th>';
        $header .= '<th rowspan="2">Total Amount</th>';
        $header .= '<th rowspan="2">Total Balance</th>';
        $header .= '<th rowspan="2">Total Fine</th>';
        $header .= '<th rowspan="2">Total Balance (Include Fine)</th>';
        $bpHeaderName = [];
        foreach ($bp as $key => $bpName) {
          $bpHeaderName[$bpName->id]['blueprint_name']=$bpName->name;
          if (isset($headerFlag[$bpName->id])) {
            $colSpan = count($headerFlag[$bpName->id]);
          }
          $header .= '<th colspan="'.$colSpan.'">'.$bpName->name.'</th>';
        }
        $header .= '</tr>';

        foreach ($headerFlag as $key => $flag) {
          foreach ($flag as $key => $val) {
            if($key == 0) $header .= '<tr>';
            $header .= '<th>'.$val['name'].'</th>';
          }
        }
        $header .= '</tr>';
        $header .= '</thead>';
      $students = [];
      foreach ($bpArry as $key => $value) {
        $students[] = $value;
      }
      return array('headers' => $headerFlag, 'students' => $students, 'header' => $header,'bpSummaryBalance'=>$bpSummaryBalance,'bpHeaderName'=>$bpHeaderName);
    }

    public function get_fee_balance_student_list_summary($student_ids, $classSectionId, $installmentId, $fee_type, $installment_type, $staff_kid, $classId, $payment_status,$donorsId){

        $this->db_readonly->select('fb.id, fb.name');
        if ($fee_type  !='all') {
          $this->db_readonly->where_in('fb.id',$fee_type);
        }
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $this->db_readonly->where('fb.acad_year_id',$this->yearId);
        $bp = $this->db_readonly->get('feev2_blueprint fb')->result();
        $this->db_readonly->select("sd.id as stdId,  fcs.blueprint_id, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(cs.class_name,''),'',ifnull(cs.section_name,'')) as class_name,  sd.admission_no,  fsi.feev2_installments_id,  ifnull(sum(fsi.installment_amount),0) as installment_amount, ifnull(sum(fsi.installment_amount_paid),0) as installment_amount_paid, ifnull(sum(fsi.total_concession_amount),0)  + ifnull(sum(fsi.total_concession_amount_paid),0) as concession, ifnull(sum(fsi.total_adjustment_amount),0)  + ifnull(sum(fsi.total_adjustment_amount_paid),0) as adjustment, fss.id as schId, ifnull(p1.first_name,'') as father_name, ifnull(p2.first_name,'') as mother_name, ifnull(p1.mobile_no,'')  as f_number, ifnull(p2.mobile_no,'') as m_number, (case when sy.promotion_status = 4 or sy.promotion_status = 5  then sy.promotion_status else sd.admission_status end) as admission_status, fb.name as fee_type, ifnull(sem_name,'NA') as semester, ifnull(sy.combination,'') as combination, SUM(ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_amount_paid,0) - ifnull(fsi.total_fine_waived,0)) as total_installment_fine, ifnull(sd.enrollment_number,'NA') as enrollment_number, ifnull(sd.student_mobile_no,'') as student_mobile_no, sd.category, sd.caste")
        ->from('student_admission sd')
        ->where_in('sd.id',$student_ids)
        ->join('student_year sy','sd.id=sy.student_admission_id')
        ->where('sy.acad_year_id',$this->yearId)
        ->join('feev2_cohort_student fcs',"sd.id=fcs.student_id")
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->where('fb.acad_year_id',$this->yearId)
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
        ->group_by('fsi.fee_student_schedule_id','sd.id')
        ->where('fss.payment_status!=','FULL')
        // ->where('sd.admission_status', 2)
        // ->where('sy.promotion_status!=', 4)
        // ->where('sy.promotion_status!=', 5)
        ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
        ->join('parent p1', 'p1.id=sr1.relation_id')
        ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
        ->join('parent p2', 'p2.id=sr2.relation_id')
        ->join('class c',"sy.class_id=c.id and c.is_placeholder!=1")
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('semester sem','sy.semester=sem.id','left');
        if ($payment_status) {
          $this->db_readonly->where_in('fss.payment_status',$payment_status);
        }
        if ($fee_type !='all') {
          $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
        }
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($classId) {
          $this->db_readonly->where_in('c.id',$classId);
        }
        if ($classSectionId) {
          $this->db_readonly->where_in('cs.id',$classSectionId);
        }
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        if ($staff_kid) {
          $this->db_readonly->where('sd.has_staff',$staff_kid);
        }
        if ($donorsId) {
          if ($donorsId == 1) {
            $this->db_readonly->where('sy.donor is null');
          }else{
            $this->db_readonly->where('sy.donor',$donorsId);
          }
        }
        $this->db_readonly->order_by('c.id, cs.id, sd.first_name');
        $result = $this->db_readonly->get()->result();
        $bpArry = [];
        $bpSummaryBalance = [];
        foreach ($result as $key => $val) {
          if (!array_key_exists($val->blueprint_id, $bpSummaryBalance)) {
            $bpSummaryBalance[$val->blueprint_id] = array();
            $bpSummaryBalance[$val->blueprint_id]['total_balance_summary'] = 0;
            $bpSummaryBalance[$val->blueprint_id]['total_amount_summary'] = 0;
          }
          if(!array_key_exists($val->stdId, $bpArry)) {
            $bpArry[$val->stdId] = array();
            $bpArry[$val->stdId]['stdId'] = $val->stdId;
            $bpArry[$val->stdId]['student_name'] = $val->student_name;
            $bpArry[$val->stdId]['class_name'] = $val->class_name;
            $bpArry[$val->stdId]['semester'] = $val->semester;
            $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
            $bpArry[$val->stdId]['combination'] = $val->combination;
            $bpArry[$val->stdId]['father_name'] = $val->father_name;
            $bpArry[$val->stdId]['mother_name'] = $val->mother_name;
            $bpArry[$val->stdId]['f_number'] = $val->f_number;
            $bpArry[$val->stdId]['m_number'] = $val->m_number;
            $bpArry[$val->stdId]['admission_status'] = $val->admission_status;
            $bpArry[$val->stdId]['enrollment_number'] = $val->enrollment_number;
            $bpArry[$val->stdId]['student_mobile_no'] = $val->student_mobile_no;
            $bpArry[$val->stdId]['fee_type'] = $val->fee_type;
            $bpArry[$val->stdId]['total_balance'] = 0;
            $bpArry[$val->stdId]['total_amount'] = 0;
            $bpArry[$val->stdId]['total_fine'] = 0;
            $bpArry[$val->stdId]['bpId_summary'][$val->blueprint_id] = 0;
            $bpArry[$val->stdId]['bpId_summary_amount'][$val->blueprint_id] = 0;
            $bpArry[$val->stdId]['caste'] = (!empty($val->caste)) ? $val->caste : 'NA';
            $bpArry[$val->stdId]['category'] = (!empty($this->settings->getSetting('category')[$val->category])) ? $this->settings->getSetting('category')[$val->category] : 'NA';
          }

          $bpSummaryBalance[$val->blueprint_id]['total_balance_summary'] += $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;
          $bpSummaryBalance[$val->blueprint_id]['total_amount_summary'] += $val->installment_amount;
          $bpArry[$val->stdId]['total_balance'] += $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;
          $bpArry[$val->stdId]['total_amount'] += $val->installment_amount;
          $bpArry[$val->stdId]['total_fine'] += $val->total_installment_fine;
          $bpArry[$val->stdId]['bpId_summary'][$val->blueprint_id] = $val->installment_amount - $val->installment_amount_paid - $val->concession - $val->adjustment;
          $bpArry[$val->stdId]['bpId_summary_amount'][$val->blueprint_id] = $val->installment_amount;
        }

        $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');
        $combinationColumn = $this->settings->getSetting('puc_combination');

        $header_summary = '<thead>';
        $header_summary .= '<tr>';
        $header_summary .= '<th rowspan="2" >#</th>';
        $header_summary .= '<th rowspan="2">Student</th>';
        $header_summary .= '<th rowspan="2">Class</th>';
        $header_summary .= '<th rowspan="2">Admission No.</th>';
        $header_summary .= '<th rowspan="2">Enrollment No.</th>';
        $header_summary .= '<th rowspan="2">Student Category</th>';
        $header_summary .= '<th rowspan="2">Student Caste</th>';
        $header_summary .= '<th rowspan="2">Student Mobile No</th>';
        if ($is_semester_scheme == 1) {
          $header_summary .= '<th rowspan="2">Semester</th>';
        }
        if($combinationColumn == 1){
          $header_summary .= '<th rowspan="2">Combination</th>';
        }
        $header_summary .= '<th rowspan="2">Father</th>';
        $header_summary .= '<th rowspan="2">Father No.</th>';
        $header_summary .= '<th rowspan="2">Mother</th>';
        $header_summary .= '<th rowspan="2">Mother No</th>';
        $header_summary .= '<th rowspan="2">Total Amount</th>';
        $header_summary .= '<th rowspan="2">Total Balance</th>';
        $header_summary .= '<th rowspan="2">Total Fine</th>';
        $header_summary .= '<th rowspan="2">Total Balance (Include Fine)</th>';
        $bpHeaderName = [];
        foreach ($bp as $key => $bpName) {
          $bpHeaderName[$bpName->id]['blueprint_name']=$bpName->name;
          $header_summary .= '<th colspan="2">'.$bpName->name.'</th>';
        }
        $header_summary .= '</tr>';

        $header_summary .= '<tr>';
        foreach ($bp as $key => $bpName) {
          $header_summary .= '<th>Fee Amount</th>';
          $header_summary .= '<th>Balance Amount</th>';
        }
        $header_summary .= '</tr>';
        $header_summary .= '</thead>';

      $students = [];
      foreach ($bpArry as $key => $value) {
        $students[] = $value;
      }
      return array('students' => $students, 'bpSummaryBalance'=>$bpSummaryBalance,'bpHeaderName'=>$bpHeaderName, 'header_summary'=>$header_summary);

    }

    public function get_fine_waiver_student($classSectionId,$fee_type,$classId){

      $this->db_readonly->select("ffw.remarks, date_format(ffw.created_on,'%d-%m-%Y') as create_date, ffw.amount, ffw.created_by, sa.admission_no, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,  concat(ifnull(c.class_name,''), '',ifnull(cs.section_name,'')) as class_section")
      ->from('feev2_fine_waiver ffw')
      ->join('feev2_cohort_student fcs','ffw.cohort_student_id=fcs.id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left');
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($fee_type) {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $result = $this->db_readonly->get()->result();
     foreach ($result as $key => $value) {
        $value->created_name = $this->_getAvatarNameById($value->created_by);
     }
     return $result;
   }

    public function get_fee_balance_student_list_sms($student_ids, $installmentId, $fee_type, $show_over_due){
      $this->db_readonly->select("sd.id as stdId, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name,  sd.admission_no, p1.first_name as father_name, p1.email as fatherEmail, p2.email as motherEmail, p2.first_name as mother_name, ifnull(p1.mobile_no,'')  as f_number, ifnull(p2.mobile_no,'') as m_number, u1.token as f_token, u2.token as m_token, sd.preferred_parent, sd.preferred_contact_no, fcs.student_id, fcs.blueprint_id,fsi.status as fee_status, fsi.feev2_installments_id, date_format(fi.end_date,'%d-%m-%Y') as due_date, if(fi.end_date<CURDATE(), 'Is Due', fsi.status) as due_status, fsi.installment_amount, fsi.installment_amount_paid, fsi.total_concession_amount, fsi.total_concession_amount_paid,  fss.discount, fss.id as schId, fi.installment_order as insFlag,  fb.name as blueprint_name, fi.name as insName,  ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0) as concession, ifnull(fsi.total_fine_amount,0) as total_fine_amount, ifnull(fsi.total_fine_amount_paid,0) as total_fine_amount_paid, ifnull(fsi.total_fine_waived,0) as total_fine_waived, ifnull(sy.combination,'') as combination")
      ->from('student_admission sd')
      ->where_in('sd.id',$student_ids)
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('feev2_cohort_student fcs',"sd.id=fcs.student_id")
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->where('fss.payment_status!=','FULL')
      ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
      ->join('parent p1', 'p1.id=sr1.relation_id')
      ->join('avatar a1', 'p1.id=a1.stakeholder_id')
      ->join('users u1', 'u1.id=a1.user_id')
      ->where('a1.avatar_type', 2)
      ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
      ->join('parent p2', 'p2.id=sr2.relation_id')
      ->join('avatar a2', 'p2.id=a2.stakeholder_id')
      ->join('users u2', 'u2.id=a2.user_id')
      ->where('a2.avatar_type', 2)
      ->join('class c',"sy.class_id=c.id and c.is_placeholder!=1")
      ->join('class_section cs','sy.class_section_id=cs.id','left');
      if ($fee_type !='all') {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      if ($installmentId) {
        $this->db_readonly->where_in('fsi.feev2_installments_id',$installmentId);
      }
      $this->db_readonly->join('feev2_installments fi','fsi.feev2_installments_id=fi.id');
      if($show_over_due){
        $today = date('Y-m-d');
        $this->db_readonly->where('date_format(fi.end_date,"%Y-%m-%d") < ', $today);
      }
      $this->db_readonly->order_by('c.id, cs.id, sd.first_name');
      $result = $this->db_readonly->get()->result();
      $bpArry = [];
      foreach ($result as $key => $val) {
        if(!array_key_exists($val->stdId, $bpArry)) {
          $bpArry[$val->stdId] = array();
          $bpArry[$val->stdId]['std_id'] = $val->stdId;
          $bpArry[$val->stdId]['student_name'] = $val->student_name;
          $bpArry[$val->stdId]['class_name'] = $val->class_name;
          $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
          $bpArry[$val->stdId]['combination'] = $val->combination;
          $bpArry[$val->stdId]['father_name'] = $val->father_name;
          $bpArry[$val->stdId]['mother_name'] = $val->mother_name;
          $bpArry[$val->stdId]['f_number'] = $val->f_number;
          $bpArry[$val->stdId]['m_number'] = $val->m_number;
          $bpArry[$val->stdId]['f_token'] = $val->f_token;
          $bpArry[$val->stdId]['m_token'] = $val->m_token;
          $bpArry[$val->stdId]['preferred_number'] = $val->preferred_contact_no;
          $bpArry[$val->stdId]['preferred_parent'] = $val->preferred_parent;
          $bpArry[$val->stdId]['fatherEmail'] = $val->fatherEmail;
          $bpArry[$val->stdId]['motherEmail'] = $val->motherEmail;
          $bpArry[$val->stdId]['message_with_due_date'] = '';
          $bpArry[$val->stdId]['message_without_due_date'] = '';
          $bpArry[$val->stdId]['balance'] = 0;
          $bpArry[$val->stdId]['balancewithfine'] = 0;
          $bpArry[$val->stdId]['concession'] = 0;
          $bpArry[$val->stdId]['fine_amount'] = 0;
          $bpArry[$val->stdId]['discount_amount'] = 0;
        }
          $bpArry[$val->stdId]['balance'] += $val->installment_amount - $val->installment_amount_paid - $val->concession;
          $bpArry[$val->stdId]['balancewithfine'] += ($val->installment_amount - $val->installment_amount_paid - $val->concession) + ($val->total_fine_amount - $val->total_fine_amount_paid - $val->total_fine_waived);
          $bpArry[$val->stdId]['message_with_due_date'] .= $val->blueprint_name .'-'.$val->insName.': '.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->concession). ' (Due Date: ' . $val->due_date . ') ';

          $bpArry[$val->stdId]['message_without_due_date'] .= $val->blueprint_name .'-'.$val->insName.': '.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->concession).' ';

        $bpArry[$val->stdId]['concession'] += ($val->total_concession_amount + $val->total_concession_amount_paid);
        $bpArry[$val->stdId]['fine_amount'] += ($val->total_fine_amount - $val->total_fine_amount_paid - $val->total_fine_waived);
        $bpArry[$val->stdId]['discount_amount'] += $val->discount;
      }
      return $bpArry;

    }
    public function get_fee_balance_student_list_sms_old($student_ids, $installmentId, $fee_type){

      $this->db_readonly->select("sd.id as stdId, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name,  sd.admission_no, p1.first_name as father_name, p1.email as fatherEmail, p2.email as motherEmail, p2.first_name as mother_name, ifnull(p1.mobile_no,'')  as f_number, ifnull(p2.mobile_no,'') as m_number, u1.token as f_token, u2.token as m_token, sd.preferred_parent, sd.preferred_contact_no")
      ->from('student_admission sd')
      ->where_in('sd.id',$student_ids)
      ->join('student_year sy','sd.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('student_relation sr1', "sr1.std_id=sd.id and sr1.relation_type='Father'")
      ->join('parent p1', 'p1.id=sr1.relation_id')
      ->join('avatar a1', 'p1.id=a1.stakeholder_id')
      ->join('users u1', 'u1.id=a1.user_id')
      ->where('a1.avatar_type', 2)
      ->join('student_relation sr2', "sr2.std_id=sd.id and sr2.relation_type='mother'")
      ->join('parent p2', 'p2.id=sr2.relation_id')
      ->join('avatar a2', 'p2.id=a2.stakeholder_id')
      ->join('users u2', 'u2.id=a2.user_id')
      ->where('a2.avatar_type', 2)
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left');

      $this->db_readonly->order_by('c.id, cs.id, sd.first_name');
      $studentData = $this->db_readonly->get()->result();

      $this->db_readonly->select("fcs.student_id, fcs.blueprint_id,fsi.status as fee_status, fsi.feev2_installments_id, date_format(fi.end_date,'%d-%m-%Y') as due_date, if(fi.end_date<CURDATE(), 'Is Due', fsi.status) as due_status, fsi.installment_amount, fsi.installment_amount_paid, fsi.total_concession_amount, fsi.total_concession_amount_paid, (ifnull(fss.total_fine_amount,0) - ifnull(fss.total_fine_amount_paid,0)) as total_fine_amount, fss.discount, fss.id as schId, fi.installment_order as insFlag,  fb.name as blueprint_name, fi.name as insName, ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0) as concession")
      ->from('feev2_cohort_student fcs')
      ->where_in('fcs.student_id',$student_ids)
      ->join('feev2_blueprint fb',"fcs.blueprint_id=fb.id and fb.acad_year_id = $this->yearId")
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->where('fss.payment_status!=','FULL');
      if ($fee_type !='all') {
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      if ($installmentId) {
        $this->db_readonly->join('feev2_student_installments fsi',"fss.id=fsi.fee_student_schedule_id");
        $this->db_readonly->where_in('fsi.feev2_installments_id',$installmentId);
      } else {
        $this->db_readonly->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id');
      }
      $this->db_readonly->join('feev2_installments fi','fsi.feev2_installments_id=fi.id');

      $feeData = $this->db_readonly->get()->result();

      foreach ($studentData as $key => &$std) {
        foreach ($feeData as $key => $fee) {
          if ($std->stdId == $fee->student_id) {
            $std->blueprint_id = $fee->blueprint_id;
            $std->fee_status = $fee->fee_status;
            $std->feev2_installments_id = $fee->feev2_installments_id;
            $std->due_date = $fee->due_date;
            $std->due_status = $fee->due_status;
            $std->installment_amount = $fee->installment_amount;
            $std->installment_amount_paid = $fee->installment_amount_paid;
            $std->total_concession_amount = $fee->total_concession_amount;
            $std->total_concession_amount_paid = $fee->total_concession_amount_paid;
            $std->total_fine_amount = $fee->total_fine_amount;
            $std->discount = $fee->discount;
            $std->schId = $fee->schId;
            $std->insFlag = $fee->insFlag;
            $std->blueprint_name = $fee->blueprint_name;
            $std->insName = $fee->insName;
            $std->concession = $fee->concession;
          }
        }
      }
      $bpArry = [];
      foreach ($studentData as $key => $val) {
        if(!array_key_exists($val->stdId, $bpArry)) {
          $bpArry[$val->stdId] = array();
          $bpArry[$val->stdId]['std_id'] = $val->stdId;
          $bpArry[$val->stdId]['student_name'] = $val->student_name;
          $bpArry[$val->stdId]['class_name'] = $val->class_name;
          $bpArry[$val->stdId]['admission_no'] = $val->admission_no;
          $bpArry[$val->stdId]['father_name'] = $val->father_name;
          $bpArry[$val->stdId]['mother_name'] = $val->mother_name;
          $bpArry[$val->stdId]['f_number'] = $val->f_number;
          $bpArry[$val->stdId]['m_number'] = $val->m_number;
          $bpArry[$val->stdId]['f_token'] = $val->f_token;
          $bpArry[$val->stdId]['m_token'] = $val->m_token;
          $bpArry[$val->stdId]['preferred_number'] = $val->preferred_contact_no;
          $bpArry[$val->stdId]['preferred_parent'] = $val->preferred_parent;
          $bpArry[$val->stdId]['fatherEmail'] = $val->fatherEmail;
          $bpArry[$val->stdId]['motherEmail'] = $val->motherEmail;
          $bpArry[$val->stdId]['message_with_due_date'] = '';
          $bpArry[$val->stdId]['message_without_due_date'] = '';
          $bpArry[$val->stdId]['balance'] = 0;
          $bpArry[$val->stdId]['concession'] = 0;
          $bpArry[$val->stdId]['fine_amount'] = 0;
          $bpArry[$val->stdId]['discount_amount'] = 0;
        }
        $bpArry[$val->stdId]['balance'] += $val->installment_amount - $val->installment_amount_paid - $val->concession;
        if ($bpArry[$val->stdId]['balance'] !=0) {
          $bpArry[$val->stdId]['message_with_due_date'] .= $val->blueprint_name .'-'.$val->insName.': '.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->concession). ' (Due Date: ' . $val->due_date . '). ';
          $bpArry[$val->stdId]['message_without_due_date'] .= $val->blueprint_name .'-'.$val->insName.': '.numberToCurrency_withoutSymbol($val->installment_amount - $val->installment_amount_paid - $val->concession).' ';
        }
        $bpArry[$val->stdId]['concession'] += ($val->total_concession_amount + $val->total_concession_amount_paid);
        $bpArry[$val->stdId]['fine_amount'] += $val->total_fine_amount;
        $bpArry[$val->stdId]['discount_amount'] += $val->discount;
      }
      return $bpArry;
    }

    public function get_student_names_all(){
      $this->db_readonly->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, cs.section_name, c.class_name, sd.id as student_id")
      ->from('student_year sy')
      ->join('student_admission sd','sy.student_admission_id=sd.id')
      // ->where('admission_status','2')
      // ->where('sy.promotion_status!=', '4')
      // ->where('sy.promotion_status!=', '5')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('class c','sy.class_id=c.id');
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $student =  $this->db_readonly->get()->result();
      return $student;
    }

    public function get_admission_number_wise_stdId($admission_no){
      $result = $this->db_readonly->select('id as stdId')->where('admission_no',$admission_no)->get('student_admission')->row();
      if (!empty($result)) {
        return $result->stdId;
      }else{
        return 0;
      }
    }

    public function view_receipt_html_fee_receipt($transId){
      return $this->db->select('receipt_html')->where('id', $transId)->get('feev2_transaction')->row()->receipt_html;
    }

    public function get_email_template_for_fee(){
      return $this->db->select('id,name')->get('email_template')->result();
    }

    public function get_sms_template_for_fee(){
      return $this->db->select('id,name')->where('category','Fees')->get('sms_template_new')->result();
    }

    public function get_fee_balance_email_templatebyId($emailtemplateId){
      return $this->db->select('*')
      ->from('email_template')
      ->where('id',$emailtemplateId)
      ->get()->row();
    }

    public function getStudents_email_fee_balance($studentIds, $sendTo){
      $this->db->select("p.id, s.id as std_id, 2 as avatar_type, CONCAT(cs.class_name,cs.section_name,' - ',ifnull(s.first_name,''), ' ', ifnull(s.last_name,''), ' (', sr.relation_type, ')') AS Name, p.mobile_no, u.id as user_id, u.token, if(u.token is null, 0, 1) as tokenState, p.email, s.preferred_parent")
      ->from('student_admission s')
      ->join('student_year sy', 'sy.student_admission_id=s.id')
      ->join('class_section cs', 'cs.id=sy.class_section_id')
      ->join('parent p', 'p.student_id=s.id')
      ->join('student_relation sr', "sr.relation_id=p.id")

      ->join('avatar a', 'a.stakeholder_id=p.id')
      ->join('users u', 'u.id=a.user_id')
      ->where('a.avatar_type','2')
      ->where('sy.acad_year_id', $this->yearId)
      ->where_in('s.id', $studentIds)
      ->order_by('cs.class_name,cs.section_name,s.first_name', 'ASC');
      if ($sendTo !='Both') {
        $this->db->where('sr.relation_type',$sendTo);
      }
      return $this->db->get()->result();
    }

    public function get_fee_management_summary($admission_status = []){

      $salesModule = $this->authorization->isModuleEnabled('SALES');
      $admission = $this->authorization->isModuleEnabled('ADMISSION');

      $status_conditions = 0;
      $alumni_conditions = 0;
      if ($admission_status ==2 ) {
        $status_conditions = "sa.admission_status = 2 and sy.promotion_status not in (4,5)";
      }
      if ($admission_status == 1) {
        $status_conditions = "sa.admission_status = 1 and sy.promotion_status not in (4,5)";
      }
      if ($admission_status == 3) {
        $status_conditions = "sa.admission_status = 3";
      }
      $this->db_readonly->select("sa.id as student_id, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status");
      $this->db_readonly->from('student_admission sa');
      $this->db_readonly->join('student_year sy', "sa.id = sy.student_admission_id");
      $this->db_readonly->where("sy.acad_year_id", $this->yearId);
      $this->db_readonly->where_not_in("sy.promotion_status", ['JOINED']);

      // if ($status_conditions) {
      //   $this->db_readonly->where($status_conditions);
      // }
      // if ($admission_status == 4 || $admission_status == 5) {
      //     $this->db_readonly->where_in('sy.promotion_status',['4','5']);
      // }
      $studentData = $this->db_readonly->get()->result();

      $studentIds = [];
      foreach ($studentData as $val) {
        if (in_array($val->admission_status, $admission_status)) {
          $studentIds[] = $val->student_id;
        }
      }
      if (empty($studentIds)) {
        return false;
      }
      // $studentIds = array_map(function($student) {

      //   return $student->student_id;
      // }, $studentData);

      $this->db_readonly->select("fb.name as bpName, fb.id as fbId, sum(fsi.installment_amount) as totalFeeAssigned, sum(fsi.installment_amount_paid) as total_fee_paid, sum(fsi.total_concession_amount) as total_concession_amount, sum(fsi.total_concession_amount_paid) as total_concession_amount_paid, ifnull(sum(fsi.total_concession_amount),0)  + ifnull(sum(fsi.total_concession_amount_paid),0) as concession, ifnull(sum(fsi.total_adjustment_amount),0)  + ifnull(sum(fsi.total_adjustment_amount_paid),0) as adjustment,  ifnull(sum(fsi.total_fine_amount),0)  - ifnull(sum(fsi.total_fine_waived),0) as fine_amount, ifnull(sum(fsi.refund_amount),0) as refund_amount")
      ->from('feev2_student_installments fsi')
      ->join('feev2_student_schedule fss','fsi.fee_student_schedule_id=fss.id')
      ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId);
       if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      $this->db_readonly->where_in('fcs.student_id', $studentIds);
      $this->db_readonly->group_by('fcs.blueprint_id');
      $fees =  $this->db_readonly->get()->result();

      $bp_due_date_amount = [];
      $feeInsOverDueData = [];
        $this->db_readonly->select("fb.id as fbId,  SUM(
        CASE
            WHEN fi.end_date <= CURRENT_DATE()
            THEN (fsi.installment_amount - IFNULL(fsi.installment_amount_paid, 0) - IFNULL(fsi.total_concession_amount, 0)
            - IFNULL(fsi.total_concession_amount_paid, 0))
            ELSE 0
        END
    ) as due_date_cross_balance_amount")
        ->from('feev2_student_installments fsi')
        ->join('feev2_student_schedule fss','fsi.fee_student_schedule_id=fss.id')
        ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->join('feev2_installments fi','fsi.feev2_installments_id =fi.id')
        ->where('fb.acad_year_id',$this->yearId);
         if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $this->db_readonly->where_in('fcs.student_id', $studentIds);
        $this->db_readonly->group_by('fcs.blueprint_id');
        $due_date_cross_blueprint_wise_bal_amount =  $this->db_readonly->get()->result();
        foreach ($due_date_cross_blueprint_wise_bal_amount as $key => $val) {
          $bp_due_date_amount[$val->fbId] = $val->due_date_cross_balance_amount;
        }

        $this->db_readonly->select("fb.id as fbId,   SUM(
        CASE
            WHEN fi.end_date <= CURRENT_DATE()
            THEN (fsi.installment_amount - IFNULL(fsi.installment_amount_paid, 0) - IFNULL(fsi.total_concession_amount, 0)
            - IFNULL(fsi.total_concession_amount_paid, 0))
            ELSE 0
        END
    ) as due_date_cross_balance_amount, fi.id as installment_id")
        ->from('feev2_student_installments fsi')
        ->join('feev2_student_schedule fss','fsi.fee_student_schedule_id=fss.id')
        ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
        ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
        ->join('feev2_installments fi','fsi.feev2_installments_id =fi.id')
        ->where('fb.acad_year_id',$this->yearId);
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $this->db_readonly->where_in('fcs.student_id', $studentIds);
        $this->db_readonly->group_by('fsi.feev2_installments_id');
        $feeIns_over_due =  $this->db_readonly->get()->result();

        foreach ($feeIns_over_due as $key => $val) {
          $feeInsOverDueData[$val->fbId][$val->installment_id] = $val->due_date_cross_balance_amount;
        }


      $this->db_readonly->select("fb.name as bpName, fb.id as fbId, sum(fsi.installment_amount) as totalFeeAssigned, sum(fsi.installment_amount_paid) as total_fee_paid, sum(fsi.total_concession_amount) as total_concession_amount, sum(fsi.total_concession_amount_paid) as total_concession_amount_paid, ifnull(sum(fsi.total_concession_amount),0)  + ifnull(sum(fsi.total_concession_amount_paid),0) as concession, ifnull(sum(fsi.total_adjustment_amount),0)  + ifnull(sum(fsi.total_adjustment_amount_paid),0) as adjustment,  ifnull(sum(fsi.total_fine_amount),0)  - ifnull(sum(fsi.total_fine_waived),0) as fine_amount, fsi.feev2_installments_id, concat(fit.name,' - ',fi.name) as ins_name, date_format(fi.end_date,'%d-%m-%Y') as due_date, fi.id as installment_id, ifnull(sum(fsi.refund_amount),0) as refund_amount")
      ->from('feev2_student_installments fsi')
      ->join('feev2_student_schedule fss','fsi.fee_student_schedule_id=fss.id')
      ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('feev2_installments fi','fsi.feev2_installments_id =fi.id')
      ->join('feev2_installment_types fit','fi.feev2_installment_type_id =fit.id')
      ->where('fb.acad_year_id',$this->yearId);
       if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      $this->db_readonly->where_in('fcs.student_id', $studentIds);

      $this->db_readonly->group_by('fsi.feev2_installments_id');
      $feeIns =  $this->db_readonly->get()->result();
      $feeInsData = [];
      foreach ($feeIns as $key => $val) {
        $feeInsData[$val->fbId][] = $val;
      }
      $this->db_readonly->select("fb.id as fbId, ifnull(sum(fss.loan_provider_charges),0) as loan_provider_charges, count(fcs.id) stdCount, ifnull(sum(fss.discount),0) as discount")
      ->from('feev2_student_schedule fss')
      ->join('feev2_cohort_student fcs','fss.feev2_cohort_student_id=fcs.id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId);
       if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      $this->db_readonly->where_in('fcs.student_id', $studentIds);
      $this->db_readonly->group_by('fb.id');
      $stdCount =  $this->db_readonly->get()->result();
      $this->db_readonly->select('fcs.student_id')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('fb.branches',$this->current_branch);
      }
      $this->db_readonly->where_in('fcs.student_id', $studentIds);
      $this->db_readonly->group_by('fcs.student_id');
      $feeQuery = $this->db_readonly->get()->result();
      $stdIds = [];
      foreach ($feeQuery as $key => $val) {
        array_push($stdIds, $val->student_id);
      }

      foreach ($fees as $key => &$val) {
        foreach ($stdCount as $key => $std) {
          if ($val->fbId == $std->fbId) {
            $val->stdCount = $std->stdCount;
            $val->loan_provider_charges = $std->loan_provider_charges;
            $val->discount = $std->discount;
          }
        }
      }
      $final_amounts = [];
      foreach ($stdCount as $item) {
        $fbId = $item->fbId;
        $discount = $item->discount;
        if (isset($bp_due_date_amount[$fbId])) {
          $due_amount = $bp_due_date_amount[$fbId];
          $final_amounts[$fbId] = $due_amount - $discount;
        }
      }

      $prevousYearId = $this->yearId - 1;
      $prevousYearname= $this->acad_year->getAcadYearById($prevousYearId);
      $previousBalance = [];
      $additionalAmount = [];
      if(!empty($stdIds)){
        $this->db_readonly->select("sum(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0)) as balance");
        $this->db_readonly->from('feev2_blueprint fb');
        $this->db_readonly->where('fb.acad_year_id',$prevousYearId);
        $this->db_readonly->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id');
        $this->db_readonly->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
        $this->db_readonly->where('fss.payment_status!=','FULL');
        $this->db_readonly->where_in('fcs.student_id',$stdIds);
        if($this->current_branch) {
          $this->db_readonly->where('fb.branches',$this->current_branch);
        }
        $previousBalance =  $this->db_readonly->get()->row();

        $additionalAmount = $this->db_readonly->select("sum(total_amount- total_used_amount - excess_refund_amount) as addtAmount")
        ->from('feev2_additional_amount fdm')
        ->where_in('fdm.student_id',$stdIds)
        ->get()->row();
      }

      $openingBalance = 0;
      if(!empty($previousBalance)  && !empty($previousBalance->balance)){
        $openingBalance = $previousBalance->balance;
      }

      $excess = 0;
      if(!empty($additionalAmount) && !empty($additionalAmount->addtAmount)){
        $excess = $additionalAmount->addtAmount;
      }

      $application = [];
      if ($admission) {
        // $applicationOnline = $this->db_readonly->select('count(af.id) as statusCount, as.form_name, sum(as.admission_fee_amount) as amountpaid')
        // ->from('online_application_fee_payment_master oafm')
        // ->where('tx_response_code',0)
        // ->join('admission_forms af','oafm.source_id=af.id')
        // ->join('admission_settings as','af.admission_setting_id=as.id')
        // ->where('as.acad_year',$this->yearId)
        // ->group_by('as.id')
        // ->get()->result();

        // $applicationOffline = $this->db_readonly->select('count(af.id) as statusCount, as.form_name, sum(at.amount) as amountpaid')
        // ->from('admission_transaction at')
        // ->join('admission_forms af','at.af_id=af.id')
        // ->join('admission_settings as','af.admission_setting_id=as.id')
        // ->where('as.acad_year',$this->yearId)
        // ->group_by('as.id')
        // ->get()->result();
        // $application = array_merge($applicationOnline, $applicationOffline);

        $application = $this->db_readonly->select('count(af.id) as statusCount, as.form_name, sum(as.admission_fee_amount) as amountpaid')
        ->from('admission_status ast')
        ->join('admission_forms af','ast.af_id=af.id')
        ->join('admission_settings as','af.admission_setting_id=as.id')
        ->where('as.acad_year',$this->yearId)
        ->where('ast.payment_status','SUCCESS')
        ->group_by('as.id')
        ->get()->result();

      }
      $sales = [];
      if ($salesModule) {
        $sales = $this->db_readonly->select('sum(st.amount) as totalAmount, ipm.product_name as product, count(quantity) as quantity')
        ->from('sales_master sm')
        ->where('sm.acad_year_id',$this->yearId)
        ->join('sales_transactions st','sm.id=st.sales_master_id')
        ->join('inventory_product_master ipm','st.inventory_product_id=ipm.id')
        ->group_by('ipm.id')
        ->get()->result();
      }
      $distrubanceCount = $this->get_fees_distrubance_student_details();
      $prevousNonContinueCount = 0;
      if(!empty($distrubanceCount)){
        $prevousNonContinueCount = $distrubanceCount['prevousNonContinueCount'];
      }
      return array('fee'=>$fees, 'application'=>$application,'sales'=>$sales,'excess'=>$excess ,'previous_bal'=>$openingBalance,'fee_installments'=>$feeInsData,'prevousYearname'=>$prevousYearname,'prevousNonContinueCount'=>$prevousNonContinueCount, 'bp_due_date_amount'=>$bp_due_date_amount,'feeInsOverDueData'=>$feeInsOverDueData);
    }

    public function _get_student_fees_details($transSchedule){
      if (empty($transSchedule)) {
        return array();
      }

      // Get unique student IDs
      $studentIds = array_unique(array_map(function($schedule) {
          return $schedule->student_id;
      }, $transSchedule));

      // Batch fetch student details
      $students = $this->db_readonly->select('
          fcs.student_id,
          CONCAT(IFNULL(sa.first_name,"")," ",IFNULL(sa.last_name,"")) as student_name,
          sa.admission_no,
          fb.name as blueprint_name
      ')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id')
      ->join('student_admission sa', 'fcs.student_id=sa.id')
      ->where_in('fcs.student_id', $studentIds)
      ->get()->result();

      // Create lookup array
      $studentLookup = array();
      foreach ($students as $student) {
          $studentLookup[$student->student_id] = $student;
      }

      foreach ($transSchedule as &$schedule) {
          if (isset($studentLookup[$schedule->student_id])) {
              $student = $studentLookup[$schedule->student_id];
              $schedule->student_name = $student->student_name;
              $schedule->admission_no = $student->admission_no;
              $schedule->blueprint_name = $student->blueprint_name;
          } else {
              $schedule->student_name = 'Unknown';
              $schedule->admission_no = 'N/A';
              $schedule->blueprint_name = 'N/A';
          }
      }

      return $transSchedule;
    }

    public function check_fees_audit_table() {
      $mismatch_results = [];

      // 1. Check Transaction vs Transaction Components mismatch
      $trans_comp_mismatch = $this->db_readonly->select('
          ft.id as trans_id,
          ft.student_id,
          ft.amount_paid as transaction_amount,
          SUM(ftc.amount_paid) as component_total,
          "Trans_Component" as queryType
      ')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_installment_component ftc', 'ft.id = ftc.fee_transaction_id')
      ->where('ft.status', 'SUCCESS')
      ->where('ft.soft_delete !=', 1)
      ->group_by('ft.id')
      ->having('transaction_amount != component_total')
      ->get()->result();

      $mismatch_results = array_merge($mismatch_results, $trans_comp_mismatch);

      // 2. Check Transaction vs Schedule mismatch
      $trans_schedule_mismatch = $this->db_readonly->select("
          fss.id as schId,
          ft.student_id,
          fss.total_fee_paid as schedule_total,
          SUM(ft.amount_paid) - SUM(IFNULL(ft.refund_amount,0)) as transaction_total,
          fss.total_fee as total_fee,
          'Trans_Schedule' as queryType
      ")
      ->from('feev2_transaction ft')
      ->join('feev2_student_schedule fss', 'ft.fee_student_schedule_id=fss.id')
      ->where('ft.status', 'SUCCESS')
      ->where('ft.soft_delete !=', 1)
      ->group_by('fss.id')
      ->having('transaction_total != schedule_total')
      ->get()->result();

      $mismatch_results = array_merge($mismatch_results, $trans_schedule_mismatch);

      // 3. Check Schedule vs Installment mismatch
      $schedule_installment_mismatch = $this->db_readonly->select("
          fss.id as schId,
          fcs.student_id,
          fss.total_fee_paid as schedule_total,
          SUM(fsi.installment_amount_paid) as installment_total,
          'Schedule_Installment' as queryType
      ")
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss', 'fcs.id = fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi', 'fss.id=fsi.fee_student_schedule_id')
      ->group_by('fss.id')
      ->having('installment_total != schedule_total')
      ->get()->result();
      $mismatch_results = array_merge($mismatch_results, $schedule_installment_mismatch);
      // 3. Check Schedule vs Installment mismatch
      $schedule_installment_components_mismatch = $this->db_readonly->select("
      fss.id as schId,
      fcs.student_id,
      fss.total_fee_paid as schedule_total,
      (SELECT SUM(IFNULL(fsi.installment_amount_paid,0))
       FROM feev2_student_installments fsi
       WHERE fsi.fee_student_schedule_id = fss.id) as installment_total,
      SUM(IFNULL(fsic.component_amount_paid,0)) as component_amount_paid,
      'Installment_Components' as queryType
      ")
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss', 'fcs.id = fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi', 'fss.id = fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic', 'fsi.id = fsic.fee_student_installment_id')
      ->group_by('fss.id')
      ->having('installment_total != component_amount_paid')
      ->get()
      ->result();
      // echo "<pre>"; print_r($this->db_readonly->last_query()); die();
      $mismatch_results = array_merge($mismatch_results, $schedule_installment_components_mismatch);

      // Get student details for all mismatches
      if (!empty($mismatch_results)) {
          $mismatch_results = $this->_get_student_fees_details($mismatch_results);
      }

      // Group results by mismatch type
      $grouped_results = [
          'Transaction and Transaction component table' => array_filter($mismatch_results, function($item) {
              return $item->queryType === 'Trans_Component';
          }),
          'Transaction and Schedule table' => array_filter($mismatch_results, function($item) {
              return $item->queryType === 'Trans_Schedule';
          }),
          'Schedule and Schedule installment Table' => array_filter($mismatch_results, function($item) {
              return $item->queryType === 'Schedule_Installment';
          }),
          'Schedule Installment and Schedule Installment Components Table' => array_filter($mismatch_results, function($item) {
              return $item->queryType === 'Installment_Components';
          })
      ];
      return $grouped_results;
  }

    public function get_total_fees_all_table(){

      // Add student details to schedule mismatches
      $shc = $this->db_readonly->select('sum(total_fee_paid) as totalFeePaid, sum(total_fee_paid) as totalFeePaid, sum(total_concession_amount_paid) as sch_con, ifnull(sum(total_fine_amount_paid),0) as sch_fine,  ifnull(sum(total_card_charge_amount),0) as sch_card_charge, ifnull(sum(discount),0) as sch_discount, ifnull(sum(refund_amount),0) as refund_amount')->get('feev2_student_schedule')->row();

      $shcIns = $this->db_readonly->select('sum(installment_amount_paid) as installmnetPaid, sum(total_concession_amount_paid) as ins_conc')->get('feev2_student_installments')->row();

      $shcComp = $this->db_readonly->select('sum(component_amount_paid) as component_amount_paid, sum(concession_amount_paid) as comp_con')->get('feev2_student_installments_components')->row();

      $transTotal = $this->db_readonly->select('sum(ft.amount_paid) as amount_paid, sum(ft.concession_amount) as trans_conession, ifnull(sum(ft.fine_amount),0) as trans_fine, ifnull(sum(ft.discount_amount),0) as trans_discount, ifnull(sum(ft.card_charge_amount),0) as trans_card_charge')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('ft.status','SUCCESS')
      ->where('ft.soft_delete!=',1)
      ->where('ftp.reconciliation_status!=',1)
      // ->where('ftp.reconciliation_status!=',3)
      ->get()->row();

      $transComp = $this->db_readonly->select('sum(ftic.amount_paid) as amount_paid, sum(ftic.concession_amount) as trans_conession_comp')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id')
      ->where('ft.status','SUCCESS')
      ->where('ft.soft_delete!=',1)
      ->where('ftp.reconciliation_status!=',1)
      // ->where('ftp.reconciliation_status!=',3)
      ->get()->row();

      $reconcile = $this->db_readonly->select('sum(ft.amount_paid) as amount_paid, sum(ft.concession_amount) as trans_conession, ifnull(sum(ft.fine_amount),0) as trans_fine, ifnull(sum(ft.discount_amount),0) as trans_discount, ifnull(sum(ft.card_charge_amount),0) as trans_card_charge')
      ->from('feev2_transaction ft')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('ftp.reconciliation_status',1)
      ->get()->row();


      // $reconcile_faild = $this->db_readonly->select('sum(ft.amount_paid) as amount_paid, sum(ft.concession_amount) as trans_conession, ifnull(sum(ft.fine_amount),0) as trans_fine, ifnull(sum(ft.discount_amount),0) as trans_discount, ifnull(sum(ft.card_charge_amount),0) as trans_card_charge')
      // ->from('feev2_transaction ft')
      // ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      // ->where('ftp.reconciliation_status',3)
      // ->get()->row();

      $fees = new stdClass();
      // Schedule Table
      $fees->totalFee = $shc->totalFeePaid;
      $fees->refund_amount = $shc->refund_amount;
      $fees->sch_con = $shc->sch_con;
      $fees->sch_fine = $shc->sch_fine;
      $fees->sch_card_charge = $shc->sch_card_charge;
      $fees->sch_discount = $shc->sch_discount;

      // Installments Table
      $fees->insAmount = $shcIns->installmnetPaid;
      $fees->ins_conc = $shcIns->ins_conc;

      // Components Table
      $fees->compAmount = $shcComp->component_amount_paid;
      $fees->comp_con = $shcComp->comp_con;
      // Transaction Table
      $fees->transTotal = $transTotal->amount_paid;
      $fees->transTotalCon = $transTotal->trans_conession;
      $fees->trans_fine = $transTotal->trans_fine;
      $fees->trans_discount = $transTotal->trans_discount;
      $fees->trans_card_charge = $transTotal->trans_card_charge;
      // Transaction Component Table
      $fees->transComp = $transComp->amount_paid;
      $fees->transCompCon = $transComp->trans_conession_comp;
      // Recocicle pending
      $fees->recon_pending_amount = $reconcile->amount_paid;
      $fees->recon_pending_concession = $reconcile->trans_conession;
      $fees->recon_pending_transfine = $reconcile->trans_fine;
      $fees->recon_pending_discount = $reconcile->trans_discount;
      $fees->recon_pending_cardCharge = $reconcile->trans_card_charge;

      // Recocicle pending
      // $fees->recon_failed_amount = $reconcile_faild->amount_paid;
      // $fees->recon_failed_concession = $reconcile_faild->trans_conession;
      // $fees->recon_failed_transfine = $reconcile_faild->trans_fine;
      // $fees->recon_failed_discount = $reconcile_faild->trans_discount;
      // $fees->recon_failed_cardCharge = $reconcile_faild->trans_card_charge;

      return $fees;

    }

    public function get_payment_issue_studentwise(){

      $shc = $this->db_readonly->query("select feev2_cohort_student_id from feev2_student_schedule fss where fss.total_fee < fss.total_fee_paid")->result();


      if (!empty($shc)) {
        $cohortStdsId = [];
        foreach ($shc as $key => $val) {
          array_push($cohortStdsId, $val->feev2_cohort_student_id);
        }
        $array = implode("','",$cohortStdsId);
        $cohortstd = $this->db_readonly->query("select student_id from feev2_cohort_student fcs where fcs.id in ('".$array."') ")->result();

        $stdIds = [];
        foreach ($cohortstd as $key => $val) {
          array_push($stdIds, $val->student_id);
        }
        $this->db_readonly->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, cs.section_name, c.class_name, sd.id as student_id")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->where_in('sd.id',$stdIds)
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return $this->db_readonly->get()->result();
      }else{
        return false;
      }
    }

    public function get_payment_issue_ins_studentwise(){
       $shc = $this->db_readonly->query("select fss.feev2_cohort_student_id from feev2_student_installments fsi join feev2_student_schedule fss on fsi.fee_student_schedule_id=fss.id where fsi.installment_amount < fsi.installment_amount_paid")->result();

      if (!empty($shc)) {
        $cohortStdsId = [];
        foreach ($shc as $key => $val) {
          array_push($cohortStdsId, $val->feev2_cohort_student_id);
        }
        $array = implode("','",$cohortStdsId);
        $cohortstd = $this->db_readonly->query("select student_id from feev2_cohort_student fcs where fcs.id in ('".$array."') ")->result();

        $stdIds = [];
        foreach ($cohortstd as $key => $val) {
          array_push($stdIds, $val->student_id);
        }
        $this->db_readonly->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, cs.section_name, c.class_name, sd.id as student_id")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->where_in('sd.id',$stdIds)
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return $this->db_readonly->get()->result();
      }else{
        return false;
      }
    }

    public function get_payment_issue_comp_studentwise(){
       $shc = $this->db_readonly->query("select fss.feev2_cohort_student_id from feev2_student_installments_components fsic join feev2_student_installments fsi on fsic.fee_student_installment_id=fsi.id join feev2_student_schedule fss on fsi.fee_student_schedule_id=fss.id where fsic.component_amount < fsic.component_amount_paid")->result();

      if (!empty($shc)) {
        $cohortStdsId = [];
        foreach ($shc as $key => $val) {
          array_push($cohortStdsId, $val->feev2_cohort_student_id);
        }
        $array = implode("','",$cohortStdsId);
        $cohortstd = $this->db_readonly->query("select student_id from feev2_cohort_student fcs where fcs.id in ('".$array."') ")->result();
        $stdIds = [];
        foreach ($cohortstd as $key => $val) {
          array_push($stdIds, $val->student_id);
        }
        $this->db_readonly->select("concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, cs.section_name, c.class_name, sd.id as student_id")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->where_in('sd.id',$stdIds)
        ->where('sy.acad_year_id',$this->yearId)
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('class c','sy.class_id=c.id');
        if($this->current_branch) {
          $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return $this->db_readonly->get()->result();
      }else{
        return false;
      }
    }

    public function get_check_receipt_number(){
      $receipts = $this->db_readonly->select('frb.*, frb.running_number as endNumber, fb.acad_year_id')
      ->from('feev2_receipt_book frb')
      ->join('feev2_blueprint fb','frb.id=fb.receipt_book_id')
      // ->where('fb.acad_year_id',$this->yearId)
      ->group_by('fb.receipt_book_id')
      ->get()->result();
      $receiptnumber = [];
      foreach ($receipts as $key => $val) {
        for ($i = 1; $i < $val->endNumber; $i++){
          $val->running_number = $i;
          $receiptnumber[] =  $this->fee_library->receipt_format_get_update($val);
        }
      }
      $fees = $this->db->select('ft.receipt_number')
      ->from('feev2_blueprint fb')
      // ->where('fb.acad_year_id',$this->yearId)
      ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id')
      ->where('ft.receipt_number!=','')
      ->get()->result();
      $feeRecipts = [];
      foreach ($fees as $key => $fee) {
        array_push($feeRecipts, $fee->receipt_number);
      }

      $sales = $this->db->select('receipt_no as receipt_number')
      ->from('sales_master')
      // ->where('acad_year_id',$this->yearId)
      ->get()->result();

      $salesRecipts = [];
      foreach ($sales as $key => $sale) {
        array_push($salesRecipts, $sale->receipt_number);
      }
      $arrayMerge =  array_merge($feeRecipts, $salesRecipts);
      // echo "<pre>rece"; print_r($receiptnumber);
      // echo "<pre>rece"; print_r($arrayMerge); die();

      // $numbers = preg_replace('/[^0-9]/', '', $arrayMerge);
      // echo "<pre>missing"; print_r($arrayMerge);
      // echo "<pre>missing"; print_r($numbers);

      // die();

      // $missing = $this->check_missing_receipt_number($numbers);
      // // die();
     return array_diff($receiptnumber,$arrayMerge);

    }

  public function check_missing_receipt_number($numbers){
    $my_array = range(min($numbers), max($numbers));
    return array_diff($my_array, $numbers);
  }

   public function get_adjustment_student_count($classSectionId,$fee_type,$classId){
      $this->db_readonly->select('fcs.id as cohort_student_id, fcs.blueprint_id, fcs.student_id, fss.id as schId, sum(ifnull(fss.total_adjustment_amount,0)) as total_adjustment_amount, sum(ifnull(fss.total_adjustment_amount_paid,0)) as total_adjustment_amount_paid, payment_status')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->order_by('c.id, cs.id, sa.first_name')
      ->group_by('fcs.id');
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($fee_type) {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $result = $this->db_readonly->get()->result();
      $total_adjust = 0;
      $studentArry = [];
      foreach ($result as $key => $val) {
        $total_adjust = $val->total_adjustment_amount + $val->total_adjustment_amount_paid;
        if ($total_adjust != 0) {
          array_push($studentArry, $val->student_id);
        }
      }
      return $studentArry;

    }

    public function get_fine_student_count($classSectionId,$fee_type,$classId){
       $this->db_readonly->select('fcs.student_id, sum(ifnull(fsi.total_fine_amount,0)) as total_fine_amount, sum(ifnull(fsi.total_fine_waived,0)) as total_fine_waived')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('student_admission sa','fcs.student_id=sa.id')
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->order_by('c.id, cs.id, sa.first_name')
      ->group_by('fsi.fee_student_schedule_id');
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($fee_type) {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $result = $this->db_readonly->get()->result();
      $total_fine = 0;
      $studentArry = [];
      foreach ($result as $key => $val) {
        $total_fine =$val->total_fine_amount;
        if ($total_fine != 0) {
          array_push($studentArry, $val->student_id);
        }
      }
      return $studentArry;
    }

    public function get_adjustment_list_new($student_id, $fee_type, $classSectionId, $classId){
      $this->db_readonly->select("fcs.id as cohort_student_id, fcs.blueprint_id, fcs.student_id, fss.id as schId, sum(ifnull(fsi.total_adjustment_amount,0)) as total_adjustment_amount, sum(ifnull(fsi.total_adjustment_amount_paid,0)) as total_adjustment_amount_paid, payment_status")
      ->from('feev2_cohort_student fcs')
      ->where_in('fcs.student_id',$student_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->group_by('fcs.id');
      if ($fee_type) {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      $schDetails  = $this->db_readonly->get()->result();
      $cohortStdIds = [];
      // $stdIds = [];
      if (!empty($schDetails)) {
        foreach ($schDetails as $key => $val) {
          array_push($cohortStdIds, $val->cohort_student_id);
          // array_push($stdIds, $val->student_id);
        }
      }
      $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as  stdName,  concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name, sa.admission_no, concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as  parent_name, c.id as cId, cs.id as csId, sa.first_name")
      ->from('student_admission sa')
      ->where_in('sa.id',$student_id)
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('student_relation sr',"sr.std_id=sa.id and sr.relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->order_by('c.id, cs.id, sa.first_name');
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $stdDetails = $this->db_readonly->get()->result();

      $adjustment = $this->db_readonly->select('fa.cohort_student_id, fa.adjustment_remarks, date_format(fa.created_on, "%d-%m-%Y") as created_on, CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as created_by')
      ->from('feev2_adjustment fa')
      ->where_in('fa.cohort_student_id', $cohortStdIds)
      ->join('avatar a',"fa.adjustment_by=a.id and a.avatar_type=4",'left')
      ->join('staff_master sm','a.stakeholder_id=sm.id','left')
      ->get()->result();
      foreach ($schDetails as $key => &$std) {
        $filterString = '';
        $createdOn = '';
        $CreatedBy = '';
        if (!empty($adjustment)) {
          foreach ($adjustment as $key => $adj) {
            if ($std->cohort_student_id == $adj->cohort_student_id) {
              if (!empty($filterString))
              $filterString = $filterString . ', ';
              $filterString = $filterString . $adj->adjustment_remarks;

              if (!empty($CreatedBy))
              $CreatedBy = $CreatedBy . ', ';
              $CreatedBy = $CreatedBy . $adj->created_by;

              if (!empty($createdOn))
              $createdOn = $createdOn . ', ';
              $createdOn = $createdOn . $adj->created_on;
            }
          }
        }
        foreach ($stdDetails as $key => $val) {
          if ($std->student_id == $val->stdId) {
            $std->stdName = $val->stdName;
            $std->stdId = $val->stdId;
            $std->class_name = $val->class_name;
            $std->cId = $val->cId;
            $std->csId = $val->csId;
            $std->first_name = $val->first_name;
            $std->admission_no = $val->admission_no;
            $std->parent_name = $val->parent_name;
            $std->created_by = '';
            $std->created_on = '';
          }
        }
        $std->adjustment_remarks = $filterString;
        $std->created_on = $createdOn;
        $std->created_by = $CreatedBy;
      }

      array_multisort(array_column($schDetails, 'cId'), SORT_ASC, array_column($schDetails, 'csId'), SORT_ASC, array_column($schDetails, 'first_name'), SORT_ASC, $schDetails);

      return $schDetails;
    }

    public function get_fine_list_new($student_id, $fee_type, $classSectionId, $classId){
        $this->db_readonly->select("fcs.id as cohort_student_id, fcs.blueprint_id, fcs.student_id, fss.id as schId, sum(ifnull(fsi.total_fine_amount,0)) as total_fine_amount, sum(ifnull(fsi.total_fine_waived,0)) as total_fine_waived, sum(ifnull(fsi.total_fine_amount_paid,0)) as total_fine_amount_paid")
      ->from('feev2_cohort_student fcs')
      ->where_in('fcs.student_id',$student_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->group_by('fsi.fee_student_schedule_id');
      if ($fee_type) {
        $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
      }
      $schDetails  = $this->db_readonly->get()->result();
      // $cohortStdIds = [];
      // // $stdIds = [];
      // if (!empty($schDetails)) {
      //   foreach ($schDetails as $key => $val) {
      //     array_push($cohortStdIds, $val->cohort_student_id);
      //   }
      // }
      $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as  stdName,  concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name, sa.admission_no, concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as  parent_name, c.id as cId, cs.id as csId, sa.first_name")
      ->from('student_admission sa')
      ->where_in('sa.id',$student_id)
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('student_relation sr',"sr.std_id=sa.id and sr.relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->order_by('c.id, cs.id, sa.first_name');
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $stdDetails = $this->db_readonly->get()->result();
      foreach ($schDetails as $key => &$std) {
        foreach ($stdDetails as $key => $val) {
          if ($std->student_id == $val->stdId) {
            $std->stdName = $val->stdName;
            $std->stdId = $val->stdId;
            $std->class_name = $val->class_name;
            $std->cId = $val->cId;
            $std->csId = $val->csId;
            $std->first_name = $val->first_name;
            $std->admission_no = $val->admission_no;
            $std->parent_name = $val->parent_name;
          }
        }
      }

      array_multisort(array_column($schDetails, 'cId'), SORT_ASC, array_column($schDetails, 'csId'), SORT_ASC, array_column($schDetails, 'first_name'), SORT_ASC, $schDetails);

      return $schDetails;
    }


    public function get_fee_paid_amount_details($blueprint, $student_id){
      $stdSch = $this->db->select('fss.id, fss.total_fee, fss.total_fee_paid, fss.payment_status, fss.discount')
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$blueprint)
      ->where('fcs.student_id',$student_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->get()->row();

      $stdInsData = $this->db->select('fsi.id, fsi.installment_amount, fsi.installment_amount_paid, fsi.status')
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$blueprint)
      ->where('fcs.student_id',$student_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->get()->result();

      $stdCompData = $this->db->select('fsic.id, fsic.component_amount, fsic.component_amount_paid')
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$blueprint)
      ->where('fcs.student_id',$student_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->get()->result();

      $stdTrans = $this->db->select('ft.id, ft.amount_paid, ft.discount_amount')
      ->from('feev2_transaction ft')
      ->where('ft.student_id',$student_id)
      ->where('ft.fee_student_schedule_id',$stdSch->id)
      ->where('ft.collected_by','0')
      ->get()->result();



      $stdTransComp = $this->db->select('ftic.id, ftic.amount_paid, ft.amount_paid as total_amount_paid')
      ->from('feev2_transaction ft')
      ->where('ft.student_id',$student_id)
      ->where('ft.fee_student_schedule_id',$stdSch->id)
      ->join('feev2_transaction_installment_component ftic','ft.id=ftic.fee_transaction_id')
      ->where('ft.collected_by','0')
      ->get()->result();

      $stdCompData = $this->db->select('fsic.id, fsic.component_amount, fsic.component_amount_paid')
      ->from('feev2_cohort_student fcs')
      ->where('fcs.blueprint_id',$blueprint)
      ->where('fcs.student_id',$student_id)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->get()->result();

      $stdTransCompMissing = $this->db->select('ft.id as fee_transaction_id, fsi.feev2_installments_id as blueprint_installments_id,
      fsi.id as fee_student_installments_id, fsic.blueprint_component_id as blueprint_component_id, "0.00" as amount_paid,  "0.00" as concession_amount, "0.00" as fine_amount, "0.00" as adjustment_amount, fsic.id as fee_student_installments_components_id, fsic.component_amount')
      ->from('feev2_transaction ft')
      ->where('ft.student_id',$student_id)
      ->where('ft.fee_student_schedule_id',$stdSch->id)
      ->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->where('fsic.id not in (select fee_student_installments_components_id from feev2_transaction_installment_component)')
      ->where('ft.collected_by','0')
      ->get()->result();

      return array('stdSch'=>$stdSch, 'stdInsData'=>$stdInsData, 'stdCompData'=>$stdCompData,'stdTransComp'=>$stdTransComp, 'stdTrans'=>$stdTrans,'stdTransCompMissing'=>$stdTransCompMissing);

    }

    public function get_student_summary_fee_data_details($student_id,$fee_type, $payment_status){
      $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as  stdName,  concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name, sa.admission_no, concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as  parent_name, c.id as cId, cs.id as csId, sa.first_name")
      ->from('student_admission sa')
      ->where_in('sa.id',$student_id)
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('student_relation sr',"sr.std_id=sa.id and sr.relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->order_by('c.id, cs.id, sa.first_name');
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $stdDetails = $this->db_readonly->get()->result();

      $this->db_readonly->select('fb.id, fb.name')
      ->from('feev2_blueprint fb')
      ->where('fb.acad_year_id',$this->yearId);
      if ($fee_type) {
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      $blueprints = $this->db_readonly->get()->result();

      foreach ($stdDetails as $key => $val) {
        $val->blueprint = $blueprints;
      }

      $this->db_readonly->select('fb.id as bpId, fb.name as blueprint_name, fcs.student_id, ifnull(fss.total_fee,0) as total_fee, ifnull(fss.total_fee_paid,0) - ifnull(fss.discount,0) as total_fee_paid, ifnull(fss.discount,0) as discount, sum(ifnull(fsi.total_concession_amount,0)) + sum(ifnull(fsi.total_concession_amount_paid,0)) as total_concession, total_card_charge_amount, sum(ifnull(fsi.refund_amount,0)) as refund_amount, ifnull(loan_provider_charges,0) as loan_charges, sum(ifnull(fsi.total_adjustment_amount,0)) - sum(ifnull(fsi.total_adjustment_amount_paid,0)) as total_adjustment, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_waived,0)) as total_fine,
        (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) - sum(ifnull(fsi.total_concession_amount,0)) - sum(ifnull(fsi.total_concession_amount_paid,0))) as balance, fss.payment_status')
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->where_in('fcs.student_id',$student_id)
      ->group_by('fsi.fee_student_schedule_id')
      ->where('fb.acad_year_id',$this->yearId);
      if ($fee_type) {
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      // if ($payment_status) {
      //   $this->db_readonly->where('fss.payment_status',$payment_status);
      // }
      $schDetails  = $this->db_readonly->get()->result();
      foreach ($stdDetails as $key => &$std) {
        $std->feeDetails = [];
        foreach ($schDetails as $key => $sch) {
          if ($std->stdId == $sch->student_id) {
            $std->feeDetails[$sch->bpId] = $sch;
          }
        }
      }
      return $stdDetails;
    }

    public function get_student_summary_fee_data_details_v2($student_id,$fee_type, $payment_status){
      $this->db_readonly->select("sa.id as stdId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as  stdName,  concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name, sa.admission_no, sa.enrollment_number as enrollment_no, sy.boarding as boarding_type, af.application_no, concat(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as  parent_name, p.mobile_no as parent_number, c.id as cId, cs.id as csId, sa.first_name")
      ->from('student_admission sa')
      ->where_in('sa.id',$student_id)
      ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id = $this->yearId")
      ->join('student_relation sr',"sr.std_id=sa.id and sr.relation_type='Father'")
      ->join('parent p','p.id=sr.relation_id')
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->join('admission_forms af','sa.admission_form_id=af.id','left')
      ->order_by('c.id, cs.id, sa.first_name');
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $stdDetails = $this->db_readonly->get()->result();

      $this->db_readonly->select('fb.id, fb.name')
      ->from('feev2_blueprint fb')
      ->where('fb.acad_year_id',$this->yearId);
      if ($fee_type) {
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      $blueprints = $this->db_readonly->get()->result();

      $boarding = $this->settings->getSetting('boarding');
      foreach ($stdDetails as $key => $val) {
        $val->blueprint = $blueprints;
        if(!empty($val->boarding_type) && isset($boarding[$val->boarding_type])){
          $val->boarding_type = $boarding[$val->boarding_type];
        }else{
          $val->boarding_type = '-';
        }

      }

      $this->db_readonly->select('fb.id as bpId, fb.name as blueprint_name, fcs.student_id, ifnull(fss.total_fee,0) as total_fee, ifnull(fss.total_fee_paid,0) - ifnull(fss.discount,0) as total_fee_paid, ifnull(fss.discount,0) as discount, sum(ifnull(fsi.total_concession_amount,0)) + sum(ifnull(fsi.total_concession_amount_paid,0)) as total_concession, total_card_charge_amount, sum(ifnull(fsi.refund_amount,0)) as refund_amount, ifnull(loan_provider_charges,0) as loan_charges, sum(ifnull(fsi.total_adjustment_amount,0)) - sum(ifnull(fsi.total_adjustment_amount_paid,0)) as total_adjustment, (ifnull(fsi.total_fine_amount,0) - ifnull(fsi.total_fine_waived,0)) as total_fine,
        (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) - sum(ifnull(fsi.total_concession_amount,0)) - sum(ifnull(fsi.total_concession_amount_paid,0))) as balance, fss.payment_status,
        SUM(
            CASE
                WHEN fi.end_date <= CURRENT_DATE()
                THEN (fsi.installment_amount - IFNULL(fsi.installment_amount_paid, 0) - IFNULL(fsi.total_concession_amount, 0)
                - IFNULL(fsi.total_concession_amount_paid, 0))
                ELSE 0
            END
        ) as total_due_amount')
      ->from('feev2_blueprint fb')
      ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->where_in('fcs.student_id',$student_id)
      ->group_by('fsi.fee_student_schedule_id')
      ->where('fb.acad_year_id',$this->yearId);
      if ($fee_type) {
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      if ($payment_status) {
        $this->db_readonly->where('fss.payment_status',$payment_status);
      }
      $schDetails  = $this->db_readonly->get()->result();
      foreach ($stdDetails as $key => &$std) {
        $std->feeDetails = [];
        foreach ($schDetails as $key => $sch) {
          if ($std->stdId == $sch->student_id) {
            $std->feeDetails[$sch->bpId] = $sch;
          }
        }
      }
      return $stdDetails;
    }

    public function get_fee_details_summary_student($classId, $fee_type, $admission_type, $payment_status){
      $this->db_readonly->select('sa.id as studentId');
      $this->db_readonly->from('student_admission sa');
      $this->db_readonly->join('student_year sy','sa.id=sy.student_admission_id');
      $this->db_readonly->where('sy.acad_year_id',$this->yearId);
      $this->db_readonly->join('class c','sy.class_id=c.id');
      $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
      $this->db_readonly->order_by('c.id, cs.id, sa.first_name');
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($admission_type) {
        $this->db_readonly->where('sy.admission_type',$admission_type);
      }
      if ($payment_status) {
        $this->db_readonly->join('feev2_cohort_student fcs','sa.id=fcs.student_id');
        $this->db_readonly->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
        $this->db_readonly->where('fss.payment_status',$payment_status);
      }
      $result = $this->db_readonly->get()->result();
      $studentIDs = [];
      foreach ($result as $key => $val) {
        array_push($studentIDs, $val->studentId);
      }
      return $studentIDs;
    }

    public function get_fee_details_summary_student_v2($classId, $fee_type, $admission_type, $payment_status, $classSectionId, $acad_year_id , $admission_status, $staff_kid, $rte_nrteId){
      $this->db_readonly->select('sa.id as studentId,(case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status');
      $this->db_readonly->from('student_admission sa');
      $this->db_readonly->join('student_year sy','sa.id=sy.student_admission_id');
      $this->db_readonly->where('sy.acad_year_id',$this->yearId);
      $this->db_readonly->join('class c','sy.class_id=c.id');
      $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
      $this->db_readonly->order_by('c.id, cs.id, sa.first_name');
      $this->db_readonly->join('feev2_cohort_student fcs','sa.id=fcs.student_id');
      $this->db_readonly->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
      $this->db_readonly->join('feev2_blueprint fb','fcs.blueprint_id=fb.id');
      $this->db_readonly->where('fb.acad_year_id',$this->yearId);
      if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($admission_type) {
        $this->db_readonly->where('sy.admission_type',$admission_type);
      }

      // Add additional filter parameters
      if ($classSectionId) {
        $this->db_readonly->where_in('cs.id',$classSectionId);
      }

      if ($acad_year_id) {
        $this->db_readonly->where_in('sa.admission_acad_year_id',$acad_year_id);
      }

      // if ($admission_status) {
      //   $this->db_readonly->where('sa.admission_status',$admission_status);
      // }

      if ($staff_kid !='all') {
        $this->db_readonly->where('sa.has_staff',$staff_kid);
      }

      if ($rte_nrteId != -1) {
        $this->db_readonly->where('sy.is_rte',$rte_nrteId);
      }

      if ($payment_status) {
       
        $this->db_readonly->where('fss.payment_status',$payment_status);
      }
      $this->db_readonly->group_by('fcs.student_id');
      $result = $this->db_readonly->get()->result();
      $studentIDs = [];
       if (!empty($admission_status)) {
        foreach ($result as $key => $val) {
          if($val->admission_status == $admission_status)
            array_push($studentIDs, $val->studentId);
          }
        }else{
          foreach ($result as $key => $val) {
            array_push($studentIDs, $val->studentId);
          }
        }
      return $studentIDs;
    }

    public function get_fees_reconciled_details($reconcilation, $from_date, $to_date){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select("ft.id as trnsId, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as stdName,  concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section, sa.admission_no, ft.concession_amount, ft.amount_paid, date_format(ft.paid_datetime,'%d-%m-%Y') as paid_date, ft.receipt_number, ftp.payment_type, allowed_payment_modes, bank_name, bank_branch, date_format(cheque_or_dd_date,'%d-%m-%Y') as cheque_or_dd_date , cheque_dd_nb_cc_dd_number, reconciliation_status, fcs.id as chortStudent_id, ifnull(ft.fine_amount,0) as fine_amount, fb.name as blueprint_name")
      ->from('feev2_cohort_student fcs')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_transaction ft','fss.id=ft.fee_student_schedule_id')
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->join('student_admission sa','ft.student_id=sa.id')
      // ->where('sa.admission_status','2')
      // ->where('sy.promotion_status!=','4')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left');
      if($reconcilation){
        $this->db_readonly->where('ftp.reconciliation_status',$reconcilation);
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $this->db_readonly->order_by('sa.id');
      $result = $this->db_readonly->get()->result();

      foreach ($result as $key => &$val) {
        $json_decode = json_decode($val->allowed_payment_modes);
        foreach ($json_decode as $key => $decode_value) {
          if ($val->payment_type == $decode_value->value) {
            $val->payment_type = $decode_value->name;
          }
        }
      }
      return $result;
    }

    public function get_payment_options_additional_amount(){
      $query = $this->db->select('*')
      ->from('feev2_additional_amount')
      ->get();

      if ($query->num_rows() > 0) {
        return 1;
      }else{
        return 0;
      }
    }

    /**
     * Get installment-wise headers for day book report
     * @param array $fee_type_id Array of fee blueprint IDs
     * @return array Grouped installment headers by blueprint
     */
    public function get_day_books_installment_headers($fee_type_id){
      $this->db_readonly->select('fi.id as installmentId, fi.name, fb.name as blueprintName, fbc.name as component_name, fb.id as bpId, fbc.id as bpCompId, CONCAT(fi.name, " - ", fbc.name) as installment_component_name');
      $this->db_readonly->from('feev2_installments fi');
      $this->db_readonly->join('feev2_installment_types fit', 'fi.feev2_installment_type_id = fit.id');
      $this->db_readonly->join('feev2_blueprint_installment_types fbit', 'fit.id = fbit.feev2_installment_type_id');
      $this->db_readonly->join('feev2_blueprint fb', 'fbit.feev2_blueprint_id = fb.id');
      $this->db_readonly->join('feev2_blueprint_components fbc', 'fb.id = fbc.feev2_blueprint_id');

      if ($fee_type_id[0] != 'All') {
      $this->db_readonly->where_in('fb.id', $fee_type_id);
      }

      if($this->current_branch) {
      $this->db_readonly->where('fb.branches', $this->current_branch);
      }

      $this->db_readonly->order_by('fb.name, fi.name, fbc.name');
      $headers_fees = $this->db_readonly->get()->result();

      // Group headers by blueprint name, then by installment-component combination
      $grouped_headers = array();
      foreach ($headers_fees as $header) {
      // Create a unique identifier for installment-component combination
      $header->installment_comp_id = $header->installmentId . '_' . $header->bpCompId;
      $header->name = $header->installment_component_name; 
      $grouped_headers[$header->blueprintName][] = $header;
      }
      // Add Excess Fees as an object, not array
      $excessObj = (object) array(
      'installmentId'=> '999',
      'name'=> 'Installment - Excess Fee',
      'blueprintName'=> 'Excess Fees',
      'component_name'=> 'Excess Fees',
      'bpId'=> '999',
      'bpCompId'=> '-4',
      'installment_component_name'=> 'Installment - Excess Fee',
      'installment_comp_id'=> '999_999',
      );
      $grouped_headers['Excess Fees'][] = $excessObj;
      return $grouped_headers;
    }

    /**
     * Get installment-wise fee transactions for day book report
     * @param array $fee_type_id Array of fee blueprint IDs
     * @param string $from_date Start date
     * @param string $to_date End date
     * @param int $classId Class filter
     * @param string $paymentModes Payment mode filter
     * @param int $include_delete Include deleted transactions
     * @param string $admission_type Admission type filter
     * @return array Transaction data with installment details
     */
    public function get_day_books_installment_wise_fees_tx($fee_type_id, $from_date, $to_date, $classId, $paymentModes, $include_delete, $admission_type){
      $fromDate = date('Y-m-d', strtotime($from_date));
      $toDate = date('Y-m-d', strtotime($to_date));

      // Get installment-component transaction data
      $this->db_readonly->select("ft.id as transId, ftic.amount_paid as amount_paid, fi.id as installmentId, fbc.id as bpCompId, CONCAT(fi.id, '_', fbc.id) as installment_comp_id");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_transaction_installment_component ftic', 'ft.id = ftic.fee_transaction_id');
      
      $this->db_readonly->join('feev2_student_installments fsi', 'ftic.fee_student_installments_id = fsi.id');
      $this->db_readonly->join('feev2_installments fi', 'fsi.feev2_installments_id = fi.id');
      $this->db_readonly->join('feev2_installment_types fit', 'fi.feev2_installment_type_id = fit.id');
      $this->db_readonly->join('feev2_blueprint_installment_types fbit', 'fit.id = fbit.feev2_installment_type_id');
      $this->db_readonly->join('feev2_blueprint fb', 'fbit.feev2_blueprint_id = fb.id');
      $this->db_readonly->join('feev2_blueprint_components fbc', 'ftic.blueprint_component_id = fbc.id');
      $this->db_readonly->join('feev2_student_schedule fss', 'fsi.fee_student_schedule_id = fss.id');

      if ($include_delete == 0) {
        $this->db_readonly->where('ft.soft_delete !=', 1);
      }
      $this->db_readonly->where('ft.status', 'SUCCESS');

      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }

      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fb.id', $fee_type_id);
      }

      if ($classId) {
        $this->db_readonly->join('feev2_cohort_student fcs', 'fss.feev2_cohort_student_id = fcs.id');
        $this->db_readonly->join('student_year sy', 'fcs.student_id = sy.student_admission_id');
        $this->db_readonly->where('sy.class_id', $classId);
        $this->db_readonly->where('sy.acad_year_id', $this->yearId);
      }

      if ($paymentModes) {
        $this->db_readonly->join('feev2_transaction_payment ftp', 'ft.id = ftp.fee_transaction_id');
        $this->db_readonly->where('ftp.payment_type', $paymentModes);
      }

      if($this->current_branch) {
        $this->db_readonly->where('fb.branches', $this->current_branch);
      }

      $this->db_readonly->group_by('ft.id, fi.id, fbc.id');
      $this->db_readonly->order_by('ft.paid_datetime', 'desc');

      $transComp = $this->db_readonly->get()->result();
      
      // Get main transaction details
      $this->db_readonly->select("ft.id as transId, ft.amount_paid as amountPaid, ft.concession_amount, ft.adjustment_amount, ft.fine_amount, ft.discount_amount, ifnull(ft.loan_provider_charges,0) as loan_provider_charges, date_format(ft.paid_datetime,'%d-%m-%Y') as receipt_date, ft.receipt_number, ftp.remarks, ft.soft_delete, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, ifnull(c.class_name,'') as class_name, ifnull(cs.section_name,'') as sectionName, ifnull(sy.semester,'') as semester, ftp.payment_type, ftp.reconciliation_status, ifnull(ftp.bank_name,'') as bank_name, ifnull(date_format(ftp.cheque_or_dd_date,'%d-%m-%Y'),'') as cheque_or_dd_date, ifnull(ftp.cheque_dd_nb_cc_dd_number,'') as cheque_dd_nb_cc_dd_number, ft.collected_by as created_by");
      $this->db_readonly->from('feev2_transaction ft');
      $this->db_readonly->join('feev2_student_schedule fss', 'ft.fee_student_schedule_id = fss.id');
      $this->db_readonly->join('feev2_cohort_student fcs', 'fss.feev2_cohort_student_id = fcs.id');
      $this->db_readonly->join('feev2_blueprint fb', 'fcs.blueprint_id = fb.id');
      $this->db_readonly->join('student_admission sa', 'fcs.student_id = sa.id');
      $this->db_readonly->join('student_year sy', 'sa.id = sy.student_admission_id  and fb.acad_year_id=sy.acad_year_id');
      $this->db_readonly->join('class c', 'sy.class_id = c.id');
      $this->db_readonly->join('class_section cs', 'sy.class_section_id = cs.id', 'left');
      $this->db_readonly->join('feev2_transaction_payment ftp', 'ft.id = ftp.fee_transaction_id');
   
      if ($include_delete == 0) {
        $this->db_readonly->where('ft.soft_delete !=', 1);
      }
      $this->db_readonly->where('ft.status', 'SUCCESS');

      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }

      if ($fee_type_id[0] != 'All') {
        $this->db_readonly->where_in('fb.id', $fee_type_id);
      }

      if ($classId) {
        $this->db_readonly->where('sy.class_id', $classId);
      }

      if ($paymentModes) {
        $this->db_readonly->where('ftp.payment_type', $paymentModes);
      }

      if ($admission_type) {
        $this->db_readonly->where('sy.admission_type', $admission_type);
      }

      if($this->current_branch) {
        $this->db_readonly->where('fb.branches', $this->current_branch);
      }

      $this->db_readonly->order_by('ft.receipt_number', 'asc');

      $transStd = $this->db_readonly->get()->result();

      // Map installment-component amounts to transactions
      foreach ($transStd as $key => &$val) {
        $val->Installments = array();
        $val->fee_type = 'Fees';
        $val->collected_name = $this->_getAvatarNameById($val->created_by);

        if (!empty($transComp)) {
          foreach ($transComp as $comp) {
            if ($val->transId == $comp->transId) {
              // Use installment_comp_id as the key for installment-component combinations
              $val->Installments[$comp->installment_comp_id] = ($val->soft_delete == 1) ? 0 : $comp->amount_paid;
              $val->amount_paid = ($val->soft_delete == 1) ? 0 : $val->amountPaid;
            }
          }
        }
      }

      return $transStd;
    }

    // public function getF2InstallmentComponents(){
    //   return $this->db->query("select * from feev2_student_installments_components")->result();
    // }

     public function component_wise_getStudentsForSummary($data)
      {

        $this->db_readonly->select("sa.id as studentId, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status")
          ->from('student_admission sa')
          ->join('student_year sy', "sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId")
          ->join('feev2_cohort_student fcs', 'sa.id=fcs.student_id')
          ->join('feev2_blueprint fb', 'fcs.blueprint_id=fb.id')
          ->where('fb.acad_year_id', $this->yearId);
        // ->where_in('sa.id',['5','35']);
        //->join('feev2_blueprint_components fbc','fb.id=fbc.feev2_blueprint_id');
        if ($data["fee_type"]) {
          if (is_array($data["fee_type"])) {
            $this->db_readonly->where_in('fb.id', $data["fee_type"]);
          } else {
            $this->db_readonly->where('fb.id', $data["fee_type"]);
          }
        }
        // if ($data["component_id"]) {
        //   $this->db_readonly->where_in('fbc.id',$data["component_id"]);
        // }
        if ($data["clsId"]) {
          $this->db_readonly->where_in('sy.class_id', $data["clsId"]);
        }
        if ($data["classSectionId"]) {
          $this->db_readonly->where_in('sy.class_section_id', $data["classSectionId"]);
        }
        if ($data["admission_type"]) {
          $this->db_readonly->where('sy.admission_type', $data["admission_type"]);
        }

        $this->db_readonly->order_by('sa.first_name', 'c.id', 'cs.id');
        $result = $this->db_readonly->get()->result();
        $studentIDs = [];
        if ($data["admission_status"]) {
          foreach ($result as $key => $val) {
            if ($data["admission_status"] == $val->admission_status) {
              array_push($studentIDs, $val->studentId);
            }
          }
        } else {
          foreach ($result as $key => $val) {
            array_push($studentIDs, $val->studentId);
          }
        }
        return $studentIDs;
    }

   public function get_component_wise_getStudentsForSummary_data($student_ids, $component_id, $fee_type, $fee_installment_id)
    {
      // First query - Fee component summary
      $this->db_readonly->select('fi.name as inatallment_name, fcs.student_id, 
          sum(fsic.component_amount) as component_amount, 
          sum(fsic.component_amount_paid) as component_amount_paid, 
          fsic.blueprint_component_id as comp_id, 
          sum(ifnull(fsic.concession_amount,0) + ifnull(fsic.concession_amount_paid,0)) as concession_amount, sum(ifnull(fss.discount, 0)) as student_discount')
        ->from('feev2_cohort_student fcs')
        ->join('feev2_student_schedule fss', 'fss.feev2_cohort_student_id=fcs.id')
        ->join('feev2_student_installments fsi', 'fss.id=fsi.fee_student_schedule_id')
        ->join('feev2_student_installments_components fsic', 'fsi.id=fsic.fee_student_installment_id')
        ->join('feev2_blueprint_components fbc', 'fbc.id=fsic.blueprint_component_id')
        ->join('feev2_blueprint fb', 'fbc.feev2_blueprint_id=fb.id')
        ->join('feev2_installments fi', 'fsi.feev2_installments_id=fi.id')
        ->where('fb.acad_year_id', $this->yearId)
        ->where_in('fcs.student_id', $student_ids)
        ->group_by('fsic.blueprint_component_id')
        ->group_by('fcs.student_id');

      // Support multiple fee types
      if ($fee_type) {
        if (is_array($fee_type)) {
          $this->db_readonly->where_in('fb.id', $fee_type);
        } 
      }

      if ($component_id) {
        $this->db_readonly->where_in('fbc.id', $component_id);
      }

      if ($fee_installment_id) {
        $this->db_readonly->where('fsi.feev2_installments_id', $fee_installment_id);
      }

      $fee_data = $this->db_readonly->get()->result();
      $feeArry = [];
      foreach ($fee_data as $val) {
        $feeArry[$val->student_id][] = $val;
      }

      // Second query - Student & component details
      $this->db_readonly->select("sa.id as student_id, sa.first_name, sa.last_name, 
          c.class_name as class, cs.section_name as section, sa.admission_no, 
          fb.name as fee_blueprint_name, fbc.name as component_name, fbc.id as comp_id, 
          ifnull(sa.enrollment_number,'-') as enrollment_number")
        ->from('student_admission sa')
        ->join('student_year sy', "sy.student_admission_id=sa.id and sy.acad_year_id = $this->yearId")
        ->join('class c', 'c.id=sy.class_id')
        ->join('class_section cs', 'cs.id=sy.class_section_id')
        ->join('feev2_cohort_student fcs', 'sa.id=fcs.student_id')
        ->join('feev2_blueprint fb', 'fb.id=fcs.blueprint_id')
        ->join('feev2_blueprint_components fbc', 'fb.id=fbc.feev2_blueprint_id')
        ->where('fb.acad_year_id', $this->yearId)
        ->where_in('sa.id', $student_ids);

      if ($fee_type) {
        if (is_array($fee_type)) {
          $this->db_readonly->where_in('fb.id', $fee_type);
        } else {
          $this->db_readonly->where('fb.id', $fee_type);
        }
      }

      if ($component_id) {
        $this->db_readonly->where_in('fbc.id', $component_id);
      }

      $student_data = $this->db_readonly->get()->result();

      $tempArry = [];
      $component_header = [];
      foreach ($student_data as $val) {
        if (!array_key_exists($val->student_id, $tempArry)) {
          $objData = new stdClass();
          $objData->student_id = $val->student_id;
          $objData->first_name = $val->first_name;
          $objData->class = $val->class;
          $objData->no_of_components = is_array($component_id) ? count($component_id) : 0;
          $objData->admission_no = $val->admission_no;
          $objData->enrollment_number = $val->enrollment_number;
          $objData->fee_blueprint_name = $val->fee_blueprint_name;
          $tempArry[$val->student_id] = $objData;
        }
        $component_header[$val->comp_id] = ['name' => $val->component_name,
                                            'blueprint_name' => $val->fee_blueprint_name];
      }

      return [
        'student_data' => $tempArry,
        'component_header' => $component_header,
        'feeArry' => $feeArry
      ];
    }


  public function component_wise_getStudentsForSummary_v2($data){

      $this->db_readonly->select("sa.id as studentId, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status")
      ->from('student_admission sa')
      ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId")
      ->join('feev2_cohort_student fcs','sa.id=fcs.student_id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId);
      // ->where_in('sa.id',['5','35']);
      //->join('feev2_blueprint_components fbc','fb.id=fbc.feev2_blueprint_id');
       if ($data["fee_type"]) {
        $this->db_readonly->where('fb.id',$data["fee_type"]);
      }
      // if ($data["component_id"]) {
      //   $this->db_readonly->where_in('fbc.id',$data["component_id"]);
      // }
      if ($data["clsId"]) {
        $this->db_readonly->where_in('sy.class_id',$data["clsId"]);
      }
      if ($data["classSectionId"]) {
        $this->db_readonly->where_in('sy.class_section_id',$data["classSectionId"]);
      }
      if ($data["admission_type"]) {
        $this->db_readonly->where('sy.admission_type',$data["admission_type"]);
      }

      $this->db_readonly->order_by('sa.first_name','c.id','cs.id');
      $result = $this->db_readonly->get()->result();
      $studentIDs = [];
      if ($data["admission_status"]) {
        foreach ($result as $key => $val) {
          if ($data["admission_status"] == $val->admission_status) {
            array_push($studentIDs, $val->studentId);
          }
        }
      }else{
         foreach ($result as $key => $val) {
          array_push($studentIDs, $val->studentId);
        }
      }
      return $studentIDs;
    }

    public function get_component_wise_getStudentsForSummary_v2_data($student_ids, $component_id, $fee_type,$fee_installment_id){
      $this->db_readonly->select('fi.name as inatallment_name,fcs.student_id, sum(fsic.component_amount) as component_amount, sum(fsic.component_amount_paid) as component_amount_paid, fsic.blueprint_component_id as comp_id, sum(ifnull(fsic.concession_amount,0)  + ifnull(fsic.concession_amount_paid,0)) as concession_amount')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fss.feev2_cohort_student_id=fcs.id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
      ->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id')
      ->join("feev2_installments fi","fsi.feev2_installments_id=fi.id")
      ->where('fb.acad_year_id',$this->yearId)
      ->where('fb.id',$fee_type)
      ->where_in('fcs.student_id',$student_ids)
      ->group_by('fsic.blueprint_component_id')
      ->group_by('fcs.student_id');
      if ($component_id) {
        $this->db_readonly->where_in('fbc.id',$component_id);
      }
      if ($fee_installment_id) {
        $this->db_readonly->where('fsi.feev2_installments_id',$fee_installment_id);
      }
      $fee_data = $this->db_readonly->get()->result();
      $feeArry = [];
      foreach ($fee_data as $key => $val) {
        $feeArry[$val->student_id][] = $val;
      }

      $this->db_readonly->select("sa.id as student_id, sa.first_name as first_name,sa.last_name as last_name, c.class_name as class, cs.section_name as section, sa.admission_no, fb.name as fee_blueprint_name, fbc.name as component_name, fbc.id as comp_id,ifnull(sa.enrollment_number,'-') as enrollment_number")
      ->from('student_admission sa')
      ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id = $this->yearId")
      ->join("class c","c.id=sy.class_id")
      ->join("class_section cs","cs.id=sy.class_section_id")
      ->join("feev2_cohort_student fcs","sa.id=fcs.student_id")
      ->join('feev2_blueprint fb','fb.id=fcs.blueprint_id')
      ->join('feev2_blueprint_components fbc','fb.id=fbc.feev2_blueprint_id')
      // ->join("feev2_blueprint_installment_types  fbit","fbit.feev2_blueprint_id=fb.id")
      // ->join("feev2_installment_types fit","fbit.feev2_installment_type_id=fit.id")
      // ->join("feev2_installments fi","fi.feev2_installment_type_id=fit.id")
      ->where('fb.id',$fee_type)
      ->where('fb.acad_year_id',$this->yearId);
      // ->order_by('fi.id',"DESC");
      if ($component_id) {
        $this->db_readonly->where_in('fbc.id',$component_id);
      }
      $this->db_readonly->where_in('sa.id',$student_ids);
      $student_data =$this->db_readonly->get()->result();

      $tempArry = [];
      $component_header = [];
      foreach ($student_data as $key => $val) {
        if (!array_key_exists($val->student_id,$tempArry)) {
          $objData = new stdClass();
          $objData->student_id = $val->student_id;
          $objData->first_name = $val->first_name;
          $objData->class = $val->class;
          $objData->no_of_components = count($component_id);
          // $objData->installment_type_name = $val->installment_type_name;
          // $objData->inatallment_name = $val->inatallment_name;
          $objData->admission_no = $val->admission_no;
          $objData->enrollment_number = $val->enrollment_number;
          $objData->fee_blueprint_name = $val->fee_blueprint_name;
          $tempArry[$val->student_id] = $objData;
        }
        $component_header[$val->comp_id] = $val->component_name;
      }
      return array('student_data'=>$tempArry, 'component_header'=>$component_header,'feeArry'=>$feeArry);
    }

    public function student_wise_component_getStudentsForSummary($data){
      $this->db_readonly->select("sa.id as studentId, (case when sy.promotion_status = 4 or sy.promotion_status = 5 then sy.promotion_status else sa.admission_status end) as admission_status")
      ->from('student_admission sa')
      ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId")
      ->join('feev2_cohort_student fcs','sa.id=fcs.student_id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->where('fb.acad_year_id',$this->yearId);

      if (!empty($data["fee_type"])) {
        $this->db_readonly->where_in('fb.id', $data["fee_type"]);
      }
      if (!empty($data["clsId"])) {
        $this->db_readonly->where_in('sy.class_id', $data["clsId"]);
      }
      if (!empty($data["classSectionId"])) {
        $this->db_readonly->where_in('sy.class_section_id', $data["classSectionId"]);
      }
      if (!empty($data["admission_type"])) {
        $this->db_readonly->where('sy.admission_type', $data["admission_type"]);
      }
      // if (isset($data["category"]) && $data["category"] !== '' && $data["category"] !== null) {
      //   $this->db_readonly->where('sa.category', $data["category"]);
      // }
      // if (isset($data["donorsId"]) && $data["donorsId"] !== '' && $data["donorsId"] !== null) {
      //   $this->db_readonly->where('sy.donor', $data["donorsId"]);
      // }
      // if (isset($data["rte_nrteId"]) && $data["rte_nrteId"] !== '' && $data["rte_nrteId"] !== null) {
      //   $this->db_readonly->where('sy.is_rte', $data["rte_nrteId"]);
      // }
      // if (isset($data["combination"]) && $data["combination"] !== '' && $data["combination"] !== null) {
      //   $this->db_readonly->where('sy.combination_id', $data["combination"]);
      // }
      // if (isset($data["staff_kid"]) && $data["staff_kid"] !== '' && $data["staff_kid"] !== null) {
      //   $this->db_readonly->where('sa.has_staff', $data["staff_kid"]);
      // }

      $this->db_readonly->order_by('sa.first_name','c.id','cs.id');
      $result = $this->db_readonly->get()->result();
      $studentIDs = [];
      if ($data["admission_status"]) {
        foreach ($result as $key => $val) {
          if ($data["admission_status"] == $val->admission_status) {
            array_push($studentIDs, $val->studentId);
          }
        }
      }else{
         foreach ($result as $key => $val) {
          array_push($studentIDs, $val->studentId);
        }
      }
      return $studentIDs;
    }

    public function get_student_wise_component_getStudentsForSummary_data($student_ids, $selectedColumns, $clsId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId, $acad_year_id, $combination, $installmentId, $admission_status, $staff_kid, $fee_type){
      // Get student basic information
      $this->db_readonly->select("sa.id as student_id, sa.first_name, sa.last_name, c.class_name as class, cs.section_name as section, sa.admission_no, ifnull(sa.enrollment_number,'-') as enrollment_number")
      ->from('student_admission sa')
      ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id = $this->yearId")
      ->join("class c","c.id=sy.class_id")
      ->join("class_section cs","cs.id=sy.class_section_id")
      ->where_in('sa.id',$student_ids);
      $student_data = $this->db_readonly->get()->result();

      // Get component-wise fee data with installment details
      $this->db_readonly->select('
        fi.name as installment_name,
        fbc.name as component_name,
        fcs.student_id,
        fsic.blueprint_component_id as comp_id,
        sum(fsic.component_amount) as component_amount,
        sum(ifnull(fsic.component_amount_paid,0)) as component_amount_paid,
        sum(ifnull(fsic.concession_amount,0) + ifnull(fsic.concession_amount_paid,0)) as concession_amount,
        sum(ifnull(fsi.total_fine_amount,0)) as fine_amount,
        (sum(fsic.component_amount) - sum(ifnull(fsic.component_amount_paid,0)) - sum(ifnull(fsic.concession_amount,0) + ifnull(fsic.concession_amount_paid,0))) as balance_amount
      ')
      ->from('feev2_cohort_student fcs')
      ->join('feev2_student_schedule fss','fss.feev2_cohort_student_id=fcs.id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_student_installments_components fsic','fsi.id=fsic.fee_student_installment_id')
      ->join('feev2_blueprint_components fbc','fbc.id=fsic.blueprint_component_id')
      ->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id')
      ->join("feev2_installments fi","fsi.feev2_installments_id=fi.id")
      ->where('fb.acad_year_id',$this->yearId)
      ->where_in('fcs.student_id',$student_ids)
      ->group_by('fcs.student_id, fsic.blueprint_component_id, fi.id')
      ->order_by('fcs.student_id, fi.id, fbc.name');

      if ($fee_type) {
        $this->db_readonly->where_in('fb.id',$fee_type);
      }
      if ($installmentId) {
        $this->db_readonly->where('fsi.feev2_installments_id',$installmentId);
      }

      $fee_data = $this->db_readonly->get()->result();
      // Organize data by student and component
      $feeArray = [];
      $componentHeaders = [];
      foreach ($fee_data as $key => $val) {
        $componentKey = $val->installment_name . ' - ' . $val->component_name;
        $componentHeaders[$componentKey] = $componentKey;
        $feeArray[$val->student_id][$componentKey] = $val;
      }

      // Prepare final student data array
      $tempArray = [];
      foreach ($student_data as $key => $val) {
        $objData = new stdClass();
        $objData->student_id = $val->student_id;
        $objData->first_name = $val->first_name;
        $objData->last_name = $val->last_name;
        $objData->class = $val->class;
        $objData->section = $val->section;
        $objData->admission_no = $val->admission_no;
        $objData->enrollment_number = $val->enrollment_number;
        $tempArray[$val->student_id] = $objData;
      }

      return array(
        'student_data' => $tempArray,
        'component_headers' => $componentHeaders,
        'feeArray' => $feeArray
      );
    }

    public function get_daily_track_fee_transaction(){

      $fromDate = date('Y-m-d', strtotime('-1 days'));
      // $fromDate = date('Y-m-d');
      // $fromDate = '2021-07-12';
      $result = $this->db_readonly->select("ft.amount_paid, ft.discount_amount, ft.concession_amount, status, ftp.reconciliation_status, transaction_mode, ftp.payment_type")
      ->from('feev2_transaction ft')
      ->where('date_format(ft.paid_datetime,"%Y-%m-%d")', $fromDate)
      ->join('feev2_transaction_payment ftp','ft.id=ftp.fee_transaction_id')
      ->where('status','SUCCESS')
      ->get()->result();
      if (empty($result)) {
        return array();
      }
      $total_concession = 0;
      $other_collected_amount = 0;
      $cash_collected_amount = 0;
      $online_collected_amount = 0;
      $totalReceipts = 0;
      $total_collection_daily = 0;
      foreach ($result as $key => $val) {
        $totalReceipts++;
        if ($val->transaction_mode == 'COUNTER') {
          if ($val->payment_type == '9') {
            $cash_collected_amount += $val->amount_paid - $val->discount_amount;
          }else{
            $other_collected_amount += $val->amount_paid - $val->discount_amount;
          }
        }
        if ($val->transaction_mode == 'ONLINE') {
          $online_collected_amount += $val->amount_paid - $val->discount_amount;
        }
        $total_concession += $val->concession_amount;
        $total_collection_daily += $val->amount_paid - $val->discount_amount;
      }
      return array('cash_collected_amount'=>$cash_collected_amount, 'other_collected_amount'=>$other_collected_amount, 'online_collected_amount'=>$online_collected_amount,'total_collection_daily'=>$total_collection_daily, 'total_concession'=>$total_concession, 'totalReceipts'=>$totalReceipts);
    }

    public function get_daily_track_enquiries_datewise(){
      $todaydate = date('Y-m-d', strtotime('-1 days'));
      return $this->db_readonly->select("count(e.id) as enquiries_today")
      ->from('enquiry e')
      ->where("DATE_FORMAT(e.created_on, '%Y-%m-%d')='$todaydate'")
      ->get()->row();
    }

    public function get_daily_track_admission_datewise(){
      $todaydate = date('Y-m-d', strtotime('-1 days'));
      // $todaydate = date('Y-m-d');
      // $totalCount =  $this->db_readonly->select('count(af.id) as statusCount, as.form_name, sum(as.admission_fee_amount) as amountpaid')
      // ->from('admission_status ast')
      // ->join('admission_forms af','ast.af_id=af.id')
      // ->join('admission_settings as','af.admission_setting_id=as.id')
      // ->where('ast.payment_status','SUCCESS')
      // ->where("DATE_FORMAT(af.created_on, '%Y-%m-%d')='$todaydate'")
      // ->get()->row();
      // echo "<pre>"; print_r($totalCount);

      $applicationOnline = $this->db_readonly->select('count(af.id) as statusCount, "ONLINE" as transaction_mode, sum(as.admission_fee_amount) as amountpaid')
      ->from('online_application_fee_payment_master oafm')
      ->where('tx_response_code',0)
      ->join('admission_forms af','oafm.source_id=af.id')
      ->join('admission_settings as','af.admission_setting_id=as.id')
      ->where("DATE_FORMAT(af.created_on, '%Y-%m-%d')='$todaydate'")
      ->get()->result();

      $applicationOffline = $this->db_readonly->select('count(af.id) as statusCount, as.form_name, sum(at.amount) as amountpaid, "COUNTER" as transaction_mode, payment_type')
      ->from('admission_transaction at')
      ->join('admission_forms af','at.af_id=af.id')
      ->join('admission_settings as','af.admission_setting_id=as.id')
      ->where("DATE_FORMAT(af.created_on, '%Y-%m-%d')='$todaydate'")
      ->get()->result();

      $application = array_merge($applicationOnline, $applicationOffline);

      $total_concession = 0;
      $other_collected_amount = 0;
      $cash_collected_amount = 0;
      $online_collected_amount = 0;
      $totalReceipts = 0;
      $total_collection_daily = 0;
      foreach ($application as $key => $val) {
        $totalReceipts += $val->statusCount;

        if ($val->transaction_mode == 'COUNTER') {
          if ($val->payment_type == '9') {
            $cash_collected_amount += $val->amountpaid;
          }else{
            $other_collected_amount += $val->amountpaid;
          }
        }
        if ($val->transaction_mode == 'ONLINE') {
          $online_collected_amount += $val->amountpaid;
        }
        $total_collection_daily += $val->amountpaid;
      }
      return array('cash_collected_amount'=>$cash_collected_amount, 'other_collected_amount'=>$other_collected_amount, 'online_collected_amount'=>$online_collected_amount,'total_collection_daily'=>$total_collection_daily, 'total_concession'=>$total_concession, 'totalReceipts'=>$totalReceipts);

    }

    public function get_email_template_management_fee(){
      return $this->db->select('*')
      ->from('email_template')
      ->where('name','email template for management fee daily track')
      ->get()->row();
    }

    function get_fee_class_daily_summary_student_details($class_ids, $fee_type, $classSectionId, $from_date, $to_date){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));

      $this->db_readonly->select('sa.id as student_id, c.class_name as class_name, c.id as clssId')
      ->from('student_admission sa')
      // ->where('sa.admission_status',2)
      ->join('student_year sy','sa.id=sy.student_admission_id')
      // ->where('sy.admission_status',2)
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->where('c.acad_year_id',$this->yearId)
      ->where_in('c.id',$class_ids);
      // ->where_in('c.id','37');
       if($this->current_branch) {
        $this->db_readonly->where('c.branch_id',$this->current_branch);
      }
      $studentData = $this->db_readonly->order_by('c.display_order')->get()->result();
      $studentArry = [];
      $studentIds = [];
      foreach ($studentData as $key => $val) {
        $studentArry[$val->student_id] = $val;
        array_push($studentIds, $val->student_id);
      }

      $transData = $this->db_readonly->select("sum(fsic.component_amount) as totalFee,  fsic.id as fsiD, sum(ifnull(fsic.refund_amount,0)) as refund_amount, fcs.student_id, sum(ifnull(fsic.adjustment_amount,0) + ifnull(fsic.adjustment_amount_paid,0)) as adjustment, fsi.id as fsiId, fcs.student_id")
      ->from('feev2_student_installments_components fsic')
      ->join('feev2_student_installments fsi','fsi.id=fsic.fee_student_installment_id')
      ->join('feev2_student_schedule fss','fsi.fee_student_schedule_id=fss.id')
      ->join('feev2_cohort_student fcs',"fcs.id=fss.feev2_cohort_student_id")
      ->where('fcs.blueprint_id',$fee_type)
      ->where_in('fcs.student_id',$studentIds)
      // ->where('fcs.student_id','1619')
      ->group_by('fcs.student_id')
      ->get()->result();
      foreach ($transData as $key => $trans) {
        if (array_key_exists($trans->student_id, $studentArry)) {
          $trans->class_name = $studentArry[$trans->student_id]->class_name;
          $trans->class_id = $studentArry[$trans->student_id]->clssId;
        }
      }
      $datewise_tras = $this->db_readonly->select('ft.student_id, sum(ft.amount_paid) as amount_paid, sum(ifnull(ft.refund_amount,0)) as refundAmount, sum(ifnull(ft.fine_amount,0)) as fine_amount, sum(ifnull(ft.discount_amount,0)) as discount_amount, sum(ifnull(ft.concession_amount,0)) as concession_amount, sum(ifnull(ft.adjustment_amount,0)) as adjustment_amount, sum(ifnull(ft.loan_provider_charges,0)) as loan_provider_charges')
      ->from('feev2_transaction ft')
      ->join('feev2_student_schedule fss','ft.fee_student_schedule_id=fss.id')
      ->join('feev2_cohort_student fcs',"fcs.id=fss.feev2_cohort_student_id")
      ->where('fcs.blueprint_id',$fee_type)
      ->where_in('ft.student_id',$studentIds)
      ->where('ft.status','SUCCESS')
      ->where('ft.soft_delete!=','1')
      ->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"')
      ->group_by('ft.student_id')
      ->get()->result();
      $transArry = [];
      foreach ($datewise_tras as $key => $val) {
        $transArry[$val->student_id] = $val;
      }
      foreach ($transData as $key => $trans) {
        $trans->paid_amount = 0;
        $trans->concession = 0;
        $trans->fine_amount = 0;
        $trans->discount = 0;
        $trans->adjustment = 0;
        $trans->loan_provider_charges = 0;
        $trans->refund_amount = 0;
        if (array_key_exists($trans->student_id, $transArry)) {
          $trans->paid_amount = $transArry[$trans->student_id]->amount_paid;
          $trans->fine_amount = $transArry[$trans->student_id]->fine_amount;
          $trans->discount = $transArry[$trans->student_id]->discount_amount;
          $trans->concession = $transArry[$trans->student_id]->concession_amount;
          $trans->adjustment = $transArry[$trans->student_id]->adjustment_amount;
          $trans->loan_provider_charges = $transArry[$trans->student_id]->loan_provider_charges;
          $trans->refund_amount = $transArry[$trans->student_id]->refundAmount;
        }
      }

      $classArry = [];
      foreach ($transData as $key => $value) {
        if (!array_key_exists($value->class_id, $classArry)) {
          $classArry[$value->class_id]['class_name'] = $value->class_name;
          $classArry[$value->class_id]['total_fee'] = 0;
          $classArry[$value->class_id]['concession'] = 0;
          $classArry[$value->class_id]['adjustment'] = 0;
          $classArry[$value->class_id]['balance'] = 0;
          $classArry[$value->class_id]['refund_amount'] = 0;
          $classArry[$value->class_id]['student_count'] = 0;
          $classArry[$value->class_id]['loan_provider_charges'] = 0;
          $classArry[$value->class_id]['fine_amount'] = 0;
          $classArry[$value->class_id]['discount'] = 0;
          $classArry[$value->class_id]['paid_amount'] = 0;
        }
        $classArry[$value->class_id]['total_fee'] +=  $value->totalFee + $value->refund_amount;
        $classArry[$value->class_id]['concession'] +=  $value->concession;
        $classArry[$value->class_id]['adjustment'] +=  $value->adjustment;
        $classArry[$value->class_id]['balance'] +=  ($value->totalFee - $value->paid_amount - $value->concession - $value->adjustment - $value->loan_provider_charges);
        $classArry[$value->class_id]['loan_provider_charges'] +=  $value->loan_provider_charges;
        $classArry[$value->class_id]['fine_amount'] +=  $value->fine_amount;
        $classArry[$value->class_id]['discount'] +=  $value->discount;
        $classArry[$value->class_id]['paid_amount'] +=  $value->paid_amount - $value->refund_amount;
        $classArry[$value->class_id]['refund_amount'] +=  $value->refund_amount;
        $classArry[$value->class_id]['student_count'] ++;
      }

      //Sort it
      $final_class_array = [];
      foreach ($class_ids as $cid) {
        foreach ($classArry as $ca_cid => $temp) {
          if ($ca_cid == $cid) {
            $final_class_array[] = $temp;
            break;
          }
        }
      }
      // echo "<pre>"; print_r(); die();
      return $final_class_array;
    }

    public function get_day_books_details_excess_amount_tx($from_date, $to_date, $classId, $paymentModes, $admission_type){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db_readonly->select("faa.id as transId,  faa.total_amount as amount_paid, '-4' as compId, '999_999' as installments, date_format(faa.created_on,'%d-%m-%Y') as receipt_date,ifnull(faa.receipt_number,0) as receipt_number,  CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, ifnull(c.class_name,'') as class_name, ifnull(cs.section_name,'') as sectionName,
      (case when  faa.payment_type is null then '9' else  faa.payment_type end) as payment_type, ifnull(faa.excess_bank_name,'')  as bank_name, faa.excess_branch as bank_branch, faa.excess_cheque_dd_nb_cc_dd_number as cheque_dd_nb_cc_dd_number, date_format( faa.excess_bank_date,'%d-%m-%Y') as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '0' as adjustment_amount, '0' as soft_delete, '0' as loan_provider_charges, ifnull(sem.sem_name,'NA') as semester, '0' as refund_amount, faa.created_by as collected_by, faa.remarks, 'Excess Fee' as fee_type, sa.id as student_id, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, ifnull(p.aadhar_no,'') as f_aadhar_no, ifnull(p.pan_number,'') as f_pan_number, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, ifnull(p1.aadhar_no,'') as m_aadhar_no, ifnull(p1.pan_number,'') as m_pan_number, p.id as father_id, CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS address, sa.admission_no, ifnull(sa.enrollment_number,'') as enrollment_number")
      ->from('feev2_additional_amount faa')
      ->join('student_year sy',"faa.student_id=sy.student_admission_id and faa.acad_year_id=sy.acad_year_id")
      ->join('student_admission sa','sy.student_admission_id = sa.id')
      ->where('sy.promotion_status!=','JOINED')
      ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
      ->join("student_relation sr1", "sr1.std_id=sa.id and sr1.relation_type='Mother'")
      ->join("parent p", "p.id=sr.relation_id")
      ->join("parent p1", "p1.id=sr1.relation_id")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','cs.id=sy.class_section_id','left')
      ->join('semester sem','sy.semester=sem.id','left')
      ->join('address_info add',"add.stakeholder_id=p.id and add.avatar_type=2 and add.address_type=1",'left');
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($admission_type != 0) {
        $this->db_readonly->where('sy.admission_type',$admission_type);
      }
      if ($paymentModes) {
        $this->db_readonly->where_in('faa.payment_type',$paymentModes);
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(faa.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $excess = $this->db_readonly->get()->result();

      foreach ($excess as $key => &$val) {
        $val->Components[$val->compId] = $val->amount_paid;
      }

      return $excess;

    }

    public function get_day_books_details_installments_excess_amount_tx($from_date, $to_date, $classId, $paymentModes, $admission_type){
      $fromDate = date('Y-m-d',strtotime($from_date));
      $toDate =date('Y-m-d',strtotime($to_date));
      $this->db_readonly->select("faa.id as transId,  faa.total_amount as amount_paid, '-4' as compId, '999_999' as installments, date_format(faa.created_on,'%d-%m-%Y') as receipt_date,ifnull(faa.receipt_number,0) as receipt_number,  CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, ifnull(c.class_name,'') as class_name, ifnull(cs.section_name,'') as sectionName,
      (case when  faa.payment_type is null then '9' else  faa.payment_type end) as payment_type, ifnull(faa.excess_bank_name,'')  as bank_name, faa.excess_branch as bank_branch, faa.excess_cheque_dd_nb_cc_dd_number as cheque_dd_nb_cc_dd_number, date_format( faa.excess_bank_date,'%d-%m-%Y') as cheque_or_dd_date, '0' as fine_amount, '0' as discount_amount, '0' as concession_amount, '0' as adjustment_amount, '0' as soft_delete, '0' as loan_provider_charges, ifnull(sem.sem_name,'NA') as semester, '0' as refund_amount, faa.created_by as collected_by, faa.remarks, 'Excess Fee' as fee_type, sa.id as student_id, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, ifnull(p.aadhar_no,'') as f_aadhar_no, ifnull(p.pan_number,'') as f_pan_number, concat(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) AS mName, ifnull(p1.aadhar_no,'') as m_aadhar_no, ifnull(p1.pan_number,'') as m_pan_number, p.id as father_id, CONCAT_WS(', ', NULLIF(Address_line1, ''), NULLIF(Address_line2, ''), NULLIF(area, ''), NULLIF(district, ''), NULLIF(state, ''), NULLIF(country, ''), NULLIF(pin_code, '')) AS address, sa.admission_no, ifnull(sa.enrollment_number,'') as enrollment_number")
      ->from('feev2_additional_amount faa')
      ->join('student_year sy',"faa.student_id=sy.student_admission_id and faa.acad_year_id=sy.acad_year_id")
      ->join('student_admission sa','sy.student_admission_id = sa.id')
      ->where('sy.promotion_status!=','JOINED')
      ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
      ->join("student_relation sr1", "sr1.std_id=sa.id and sr1.relation_type='Mother'")
      ->join("parent p", "p.id=sr.relation_id")
      ->join("parent p1", "p1.id=sr1.relation_id")
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','cs.id=sy.class_section_id','left')
      ->join('semester sem','sy.semester=sem.id','left')
      ->join('address_info add',"add.stakeholder_id=p.id and add.avatar_type=2 and add.address_type=1",'left');
      if ($classId) {
        $this->db_readonly->where_in('c.id',$classId);
      }
      if ($admission_type != 0) {
        $this->db_readonly->where('sy.admission_type',$admission_type);
      }
      if ($paymentModes) {
        $this->db_readonly->where_in('faa.payment_type',$paymentModes);
      }
      if ($fromDate && $toDate) {
        $this->db_readonly->where('date_format(faa.created_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
      }
      $excess = $this->db_readonly->get()->result();

      foreach ($excess as $key => &$val) {
        $val->Installments[$val->installments] = $val->amount_paid;
      }

      return $excess;

    }

    public function fee_student_history_data_by_stdbyId($stdId){
      $stdDetails = $this->db_readonly->select("concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name, sd.admission_no")
      ->from('student_admission sd')
      ->where('sd.id',$stdId)
      ->join('student_year sy',"sd.id=sy.student_admission_id")
      ->where('sy.acad_year_id',$this->yearId)
      ->join('class c','sy.class_id=c.id')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->get()->row();

      $feeDetails = $this->db_readonly->select("fb.name as blueprint_name, fss.total_fee as fee_amount,  ifnull(fss.total_fee_paid,0) - ifnull(fss.discount,0) as paid_amount, (ifnull(fss.total_concession_amount,0)  + ifnull(fss.total_concession_amount_paid,0)) as concession, (ifnull(fss.total_adjustment_amount,0)  + ifnull(fss.total_adjustment_amount_paid,0)) as adjustment, (ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) ) as balance, fss.id as schId")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$stdId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      // ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      ->order_by('fb.acad_year_id')
      // ->where('fb.acad_year_id',$this->yearId)
      ->get()->result();

      $installments = $this->db_readonly->select("concat(fi.name,'- ', fb.name) as insName, ifnull(fsi.installment_amount,0) as installment_amount, ifnull(fsi.installment_amount_paid,0) as installment_amount_paid, ifnull(fsi.total_concession_amount,0) as total_concession_amount, ifnull(fsi.total_concession_amount_paid,0) total_concession_amount_paid, (ifnull(fsi.total_concession_amount,0)  + ifnull(fsi.total_concession_amount_paid,0)) as concession, (ifnull(fsi.installment_amount,0) - ifnull(fsi.installment_amount_paid,0) -  ifnull(fsi.total_concession_amount,0)  - ifnull(fsi.total_concession_amount_paid,0) ) as balance, fsi.fee_student_schedule_id, if(fi.end_date<CURDATE(), 'Is Due', fsi.status) as due_status, ifnull(fsi.total_adjustment_amount,0) as total_adjustment_amount, ifnull(fsi.total_adjustment_amount_paid,0) total_adjustment_amount_paid, (ifnull(fsi.total_adjustment_amount,0)  + ifnull(fsi.total_adjustment_amount_paid,0)) as adjustment")
      ->from('feev2_cohort_student fcs')
      ->where('fcs.student_id',$stdId)
      ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
      ->join('feev2_student_installments fsi','fss.id=fsi.fee_student_schedule_id')
      ->join('feev2_installments fi','fsi.feev2_installments_id=fi.id')
      ->join('feev2_blueprint fb','fcs.blueprint_id=fb.id')
      // ->where('fb.acad_year_id',$this->yearId)
      ->order_by('fb.acad_year_id')
      ->get()->result();
      $insArry =[];
      foreach ($installments as $key => $ins) {
        $insArry[$ins->fee_student_schedule_id][] = $ins;
      }
      return array('student'=>$stdDetails, 'fees'=>$feeDetails,'installments'=>$insArry);
      // echo "<pre>"; print_r($result); die();
    }

    public function fee_student_history_data_by_stdbyId_v2($stdId) {
      $stdDetails = $this->db_readonly->select([
              "CONCAT(IFNULL(sd.first_name,''), ' ', IFNULL(sd.last_name,'')) as student_name",
              "CONCAT(IFNULL(c.class_name,''), ' ', IFNULL(cs.section_name,'')) as class_name",
              "sd.admission_no"
          ])
          ->from('student_admission sd')
          ->join('student_year sy', 'sd.id = sy.student_admission_id')
          ->join('class c', 'sy.class_id = c.id')
          ->join('class_section cs', 'sy.class_section_id = cs.id', 'left')
          ->where([
              'sd.id' => $stdId,
              'sy.acad_year_id' => $this->yearId
          ])
          ->get()->row();

      $feeInstallments = $this->db_readonly->select([
              "fb.name as blueprint_name",
              "fss.total_fee as fee_amount",
              "IFNULL(fss.total_fee_paid, 0) - IFNULL(fss.discount, 0) as paid_amount",
              "(IFNULL(fss.total_concession_amount, 0) + IFNULL(fss.total_concession_amount_paid, 0)) as concession",
              "(IFNULL(fss.total_adjustment_amount, 0) + IFNULL(fss.total_adjustment_amount_paid, 0)) as adjustment",
              "(IFNULL(fss.total_fee, 0) - IFNULL(fss.total_fee_paid, 0) - IFNULL(fss.total_concession_amount, 0) - IFNULL(fss.total_concession_amount_paid, 0)) as balance",
              "fss.id as fee_student_schedule_id",

              "CONCAT(fi.name, '- ', fb.name) as insName",
              "IFNULL(fsi.installment_amount, 0) as installment_amount",
              "IFNULL(fsi.installment_amount_paid, 0) as installment_amount_paid",
              "IFNULL(fsi.total_concession_amount, 0) as total_concession_amount",
              "IFNULL(fsi.total_concession_amount_paid, 0) as total_concession_amount_paid",
              "(IFNULL(fsi.total_concession_amount, 0) + IFNULL(fsi.total_concession_amount_paid, 0)) as installment_concession",
              "(IFNULL(fsi.installment_amount, 0) - IFNULL(fsi.installment_amount_paid, 0) - IFNULL(fsi.total_concession_amount, 0) - IFNULL(fsi.total_concession_amount_paid, 0)) as installment_balance",
              "IF(fi.end_date < CURDATE(), 'Is Due', fsi.status) as due_status",
              "IFNULL(fsi.total_adjustment_amount, 0) as total_adjustment_amount",
              "IFNULL(fsi.total_adjustment_amount_paid, 0) as total_adjustment_amount_paid",
              "(IFNULL(fsi.total_adjustment_amount, 0) + IFNULL(fsi.total_adjustment_amount_paid, 0)) as installment_adjustment",
              "IFNULL(fsi.total_fine_amount, 0) as total_fine_amount",
              "IFNULL(fsi.total_fine_amount_paid, 0) as total_fine_amount_paid",
              "IFNULL(fsi.total_fine_waived, 0) as total_fine_waived",
              "fsi.refund_amount"
          ])
          ->from('feev2_cohort_student fcs')
          ->join('feev2_student_schedule fss', 'fcs.id = fss.feev2_cohort_student_id')
          ->join('feev2_student_installments fsi', 'fss.id = fsi.fee_student_schedule_id')
          ->join('feev2_installments fi', 'fsi.feev2_installments_id = fi.id')
          ->join('feev2_blueprint fb', 'fcs.blueprint_id = fb.id')
          ->where('fcs.student_id', $stdId)
          ->order_by('fb.acad_year_id, fb.name')
          ->get()->result();

      $fees = [];
      $installments = [];

      foreach ($feeInstallments as $row) {
          $scheduleId = $row->fee_student_schedule_id;

          if (!isset($fees[$scheduleId])) {
              $fees[$scheduleId] = (object)[
                  'blueprint_name' => $row->blueprint_name,
                  'fee_amount' => $row->fee_amount,
                  'paid_amount' => $row->paid_amount,
                  'concession' => $row->concession,
                  'adjustment' => $row->adjustment,
                  'balance' => $row->balance,
                  'schId' => $scheduleId,
              ];
          }

          $installments[$scheduleId][] = (object)[
              'insName' => $row->insName,
              'installment_amount' => $row->installment_amount,
              'installment_amount_paid' => $row->installment_amount_paid,
              'total_concession_amount' => $row->total_concession_amount,
              'total_concession_amount_paid' => $row->total_concession_amount_paid,
              'concession' => $row->installment_concession,
              'balance' => $row->installment_balance,
              'due_status' => $row->due_status,
              'total_adjustment_amount' => $row->total_adjustment_amount,
              'total_adjustment_amount_paid' => $row->total_adjustment_amount_paid,
              'adjustment' => $row->installment_adjustment,
              'total_fine_amount' => $row->total_fine_amount,
              'total_fine_amount_paid' => $row->total_fine_amount_paid,
              'total_fine_waived' => $row->total_fine_waived,
              'refund_amount' => $row->refund_amount,
          ];
      }

      $transactions = $this->db_readonly->select([
              "ft.id", "ft.student_id", "ft.amount_paid", "ft.paid_datetime",
              "ft.fine_amount", "ft.discount_amount", "ft.concession_amount",
              "ft.receipt_pdf_link", "ft.receipt_number", "ft.pdf_status",
              "ft.fee_student_schedule_id", "ftp.payment_type",
              "ft.op_recon_status as reconciliation_status",
              "ftp.recon_submitted_on"
          ])
          ->from('feev2_transaction ft')
          ->join('feev2_transaction_payment ftp', 'ft.id = ftp.fee_transaction_id')
          ->where([
              'ft.soft_delete !=' => 1,
              'ft.status' => 'SUCCESS',
              'ft.student_id' => $stdId
          ])
          ->get()->result();

      $txnArry = [];
      foreach ($transactions as $txn) {
          $txnArry[$txn->fee_student_schedule_id][] = $txn;
      }

      return [
          'student' => $stdDetails,
          'fees' => $fees,
          'installments' => $installments,
          'transactions' => $txnArry
      ];
    }
    
    public function get_predefined_filters(){
      return $this->db->select('id,saved_report_name as title')->from('predefined_reports')->where('master_report_name ','daily_transaction_filter_selections')->get()->result();
    }

    public function get_predefined_filters_by_id($filter_id){
      return $this->db->select('id,filters_selected')->from('predefined_reports')->where('id',$filter_id)->where('master_report_name ','daily_transaction_filter_selections')->get()->row();
    }

    public function get_predefined_filters1(){//fee_detail_report
      return $this->db->select('id,saved_report_name as title')->from('predefined_reports')->where('master_report_name ','fee_detail_filter_selections')->get()->result();
    }

    public function get_predefined_filters_by_id1($filter_id){//fee_detail_report
      return $this->db->select('id,filters_selected')->from('predefined_reports')->where('id',$filter_id)->where('master_report_name ','fee_detail_filter_selections')->get()->row();
    }

    public function get_predefined_filters2(){//balance_sms
      return $this->db->select('id,saved_report_name as title')->from('predefined_reports')->where('master_report_name ','sms_report_filter_selections')->get()->result();
    }

    public function get_predefined_filters_by_id2($filter_id){//balance_sms
      return $this->db->select('id,filters_selected')->from('predefined_reports')->where('id',$filter_id)->where('master_report_name ','sms_report_filter_selections')->get()->row();
    }

    public function get_predefined_filters3(){//management_summary
      return $this->db->select('id,saved_report_name as title')->from('predefined_reports')->where('master_report_name ','management_summary_filter_selections')->get()->result();
    }

    public function get_predefined_filters_by_id3($filter_id){//management_summary
      return $this->db->select('id,filters_selected')->from('predefined_reports')->where('id',$filter_id)->where('master_report_name ','management_summary_filter_selections')->get()->row();
    }

    public function get_predefined_filters4(){//fee summary detail report
      return $this->db->select('id,saved_report_name as title')->from('predefined_reports')->where('master_report_name ','student_fees_summary_v2_filter_selections')->get()->result();
    }

    public function get_predefined_filters_by_id4($filter_id){//fee summary detail report
      return $this->db->select('id,filters_selected')->from('predefined_reports')->where('id',$filter_id)->where('master_report_name ','student_fees_summary_v2_filter_selections')->get()->row();
    }

    public function get_predefined_filters5(){//management_day_wise_summary
      return $this->db->select('id,saved_report_name as title')->from('predefined_reports')->where('master_report_name ','management_day_wise_summary')->get()->result();
    }

    public function get_predefined_filters_by_id5($filter_id){//management_day_wise_summary
      return $this->db->select('id,filters_selected')->from('predefined_reports')->where('id',$filter_id)->where('master_report_name ','management_day_wise_summary')->get()->row();
    }

    public function get_fees_edit_history_data($from_date,$to_date,$student_name){
      // $class_section_id = explode('_',$cls_section);
      // $class_id = $class_section_id[0];
      // $section_id = $class_section_id[1];
       $this->db->select("fh.*,ifnull(old_value,'-') as old_value,date_format(fh.edited_on,'%d %M %Y') as edited_on,concat(ifnull(sa.first_name,''),' ', ifnull(sa .last_name,'')) as student_name,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as edited_by")
      ->from('fees_edit_history fh')
      ->join('staff_master sm','fh.edited_by = sm.id','left')
      ->join('student_admission sa','fh.student_id=sa.id')
      ->join('student_year sy','sa.id=sy.student_admission_id')
      ->join('class c','sy.class_id=c.id','left')
      ->join('class_section cs','sy.class_section_id=cs.id','left')
      ->where('sy.acad_year_id', $this->yearId);
      if(empty($student_name)){
        $this->db->where('date_format(fh.edited_on,"%Y-%m-%d") between "'.date('Y-m-d',strtotime($from_date)).'" and "'.date('Y-m-d',strtotime($to_date)).'" ');
      }
      if($student_name){
        $this->db->like('sa.first_name',$student_name);
        $this->db->or_like('sa.last_name',$student_name);
      }
      // if(!empty($stud_id)){
      //   $this->db->where('sa.id',$stud_id);
      // }
      // if(!empty($class_id)){
      //   $this->db->where('c.id',$class_id);
      // }
      // if(!empty($section_id)){
      //   $this->db->where('cs.id',$section_id);
      // }
      $res =  $this->db->get()->result();
      // echo '<pre>';print_r($res);die();

      return $res;
    }

    public function get_fees_balance_student_names(){
	    $student =  $this->db->select("sd.id as student_id, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as s_name, ifnull(cs.section_name,'') as section_name, REPLACE(REPLACE(c.class_name, '(', ''), ')', '') as class_name")
        ->from('student_year sy')
        ->join('student_admission sd','sy.student_admission_id=sd.id')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id', 'left')
        ->where('sd.admission_status','2')
        ->where('sy.promotion_status!=', '4')
        ->where('sy.promotion_status!=', '5')
        ->where('c.is_placeholder!=', '1')
        ->where('sy.acad_year_id',$this->yearId)
        ->get()->result();
	    return $student;
	}

  public function get_class_section_by_fees_selection_classbyId($feeclassID){
    return $this->db->select("c.id as class_id, cs.id as section_id, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section")
    ->from('class c')
    ->join('class_section cs','c.id=cs.class_id')
    ->where_in('c.id',$feeclassID)
    ->order_by('cs.display_order','asc')
    ->get()->result();

  }

  public function get_fees_cheque_details($input){
    // echo "<pre>"; print_r($input);die();
    $this->db_readonly->select('ft.amount_paid, ft.acad_year_id, bank_name, bank_branch, ftp.cheque_dd_nb_cc_dd_number, ftp.payment_gateway_info, ftp.canceled_remarks, ftp.payment_type, ft.concession_amount, ft.receipt_pdf_link, ft.receipt_number, ft.status, ftp.cheque_or_dd_date, ftp.recon_created_on, ftp.remarks, sd.admission_no, CONCAT(IFNULL(sd.first_name, ""), " ", IFNULL(sd.last_name, "")) AS stdName,CONCAT(IFNULL(cs.class_name, ""), " ", IFNULL(cs.section_name, "")) AS class_section');
    $this->db_readonly->from('feev2_transaction ft');
    $this->db_readonly->join('feev2_transaction_payment ftp', 'ft.id = ftp.fee_transaction_id');
    $this->db_readonly->join('student_admission sd', 'ft.student_id = sd.id');
    $this->db_readonly->join('student_year sy', 'sd.id = sy.student_admission_id');
    $this->db_readonly->join('class_section cs', 'cs.id = sy.class_section_id');
    if ($input['from_date'] && $input['to_date']) {
    $this->db_readonly->where('date_format(ftp.cheque_or_dd_date,"%d-%m-%Y") BETWEEN "'.$input['from_date']. '" and "'.$input['to_date'].'"');
    }
    $this->db_readonly->where('ftp.payment_type',$input['modeOfPayment']);
    $this->db_readonly->where('ft.status','SUCCESS');
    $this->db_readonly->order_by('ftp.cheque_or_dd_date', 'DESC');
    $result= $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($result);die();
    return $result;
  }

  public function get_fee_blueprint_cheques(){
    $this->db_readonly->select('id,name');
    $this->db_readonly->from('feev2_blueprint');
    $this->db_readonly->where('acad_year_id',$this->yearId);
    $result= $this->db_readonly->get()->result();
    // echo "<pre>"; print_r($result); die();
    return $result;
  }

  public function get_allowed_payment_modes($input){
    // echo "<pre>"; print_r($input['blueprint_selector']); die();
    $this->db_readonly->select('id,allowed_payment_modes');
    $this->db_readonly->from('feev2_blueprint');
    $this->db_readonly->where('id',$input['blueprint_selector']);
    $result= $this->db_readonly->get()->result();
    return json_decode($result[0]->allowed_payment_modes);
  }


public function get_fees_distrubance_student_details(){
  $prevousYearId = $this->yearId - 1;
  $prevousYearname= $this->acad_year->getAcadYearById($prevousYearId);

  $stdIdsprevious = $this->db_readonly->select("fcs.student_id")
  ->from('feev2_blueprint fb')
  ->where('fb.acad_year_id',$prevousYearId)
  ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
  ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
  ->where('fss.payment_status!=','FULL')
  ->group_by('student_id')
  ->get()->result();
  $preStdsIds = [];
  foreach ($stdIdsprevious as $key => $val) {
    array_push($preStdsIds, $val->student_id);
  }

  $stdIdsCurrent = $this->db_readonly->select("fcs.student_id")
  ->from('feev2_blueprint fb')
  ->where('fb.acad_year_id',$this->yearId)
  ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
  ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
  // ->where('fss.payment_status!=','FULL')
  ->group_by('student_id')
  ->get()->result();

  $curStdsIds = [];
  foreach ($stdIdsCurrent as $key => $val) {
    array_push($curStdsIds, $val->student_id);
  }

  $difference = array_diff($preStdsIds, $curStdsIds);
  $prevousNonContinueCount = 0;
  $studentData = [];
  if(!empty($difference)){
    $this->db_readonly->select("fcs.student_id, sum(ifnull(fss.total_fee,0) - ifnull(fss.total_fee_paid,0) -  ifnull(fss.total_concession_amount,0)  - ifnull(fss.total_concession_amount_paid,0) - ifnull(fss.total_adjustment_amount,0) - ifnull(fss.total_adjustment_amount_paid,0)) as balance");
    $this->db_readonly->from('feev2_blueprint fb');
    $this->db_readonly->where('fb.acad_year_id',$prevousYearId);
    $this->db_readonly->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id');
    $this->db_readonly->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id');
    $this->db_readonly->where('fss.payment_status!=','FULL');
    $this->db_readonly->where_in('fcs.student_id',$difference);
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    $this->db_readonly->group_by('fcs.student_id');
    $previousBalance =  $this->db_readonly->get()->result();

    foreach ($previousBalance as $key => $val) {
      $prevousNonContinueCount += $val->balance;
    }
    // echo "<pre>"; print_r($prevousNonContinueCount);
    // echo "<pre>"; print_r($previousBalance);
    // die();
    $this->db_readonly->select("sd.id as stdId, concat(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name, concat(ifnull(c.class_name,''),'',ifnull(cs.section_name,'')) as class_name,  sd.admission_no")
    ->from('student_admission sd')
    ->where_in('sd.id',$difference)
    ->join('student_year sy','sd.id=sy.student_admission_id')
    ->join('class c','sy.class_id=c.id')
    ->join('class_section cs','sy.class_section_id=cs.id','left');
    $this->db_readonly->where('sy.acad_year_id',$prevousYearId);
    $this->db_readonly->order_by('c.id, cs.id, sd.first_name');
    $studentData = $this->db_readonly->get()->result();

    foreach ($studentData as $key => $data) {
        foreach ($previousBalance as $key => $value) {
          if($data->stdId == $value->student_id){
            $data->balance = $value->balance;
          }
        }
    }
  }


  return array('studentData'=>$studentData, 'prevousNonContinueCount'=>$prevousNonContinueCount);
}

  public function get_fee_invoice_details_headers($fee_type){
    $this->db_readonly->select('fbc.id as bpCompId, fbc.name, fb.name as blueprintName, fb.id as bpId');
    $this->db_readonly->from('feev2_blueprint_components fbc');
    if ($fee_type) {
      $this->db_readonly->where_in('fbc.feev2_blueprint_id',$fee_type);
    }
    $this->db_readonly->join('feev2_blueprint fb','fbc.feev2_blueprint_id=fb.id');
    if($this->current_branch) {
      $this->db_readonly->where('fb.branches',$this->current_branch);
    }
    $headers_fees =  $this->db_readonly->get()->result();
    $headers = [];
    foreach ($headers_fees as $key => $val) {
      $headers[$val->blueprintName][] = $val;
    }
    return $headers;
  }

  public function get_fees_invoice_details_fees($fee_type, $classId, $admission_type){

    $this->db_readonly->select("sa.id as student_admission_id, sa.admission_no, concat(sa.first_name, ' ',ifnull(sa.last_name,'')) as std_name, concat(c.class_name, '',ifnull(cs.section_name,'')) as class_section, c.class_name as className, cs.section_name as sectionName, fcs.id as cohort_student_id, fss.id as student_schedule_id, date_format(fss.created_on,'%d-%m-%Y') as booking_date")
    ->from('student_admission sa')
    ->join('student_year sy','sa.id = sy.student_admission_id')
    ->join('class c','sy.class_id = c.id')
    ->join('class_section cs','sy.class_section_id = cs.id','left')
    ->join('feev2_cohort_student fcs','fcs.student_id = sa.id')
    ->join('feev2_student_schedule fss','fcs.id = fss.feev2_cohort_student_id')
    ->join('feev2_blueprint fb','fb.id = fcs.blueprint_id')
    // ->where('sa.id','35')
    ->where('sy.acad_year_id',$this->yearId)
    ->where('fb.acad_year_id',$this->yearId);
    if ($fee_type) {
      $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
    }
    if ($classId) {
      $this->db_readonly->where('c.id',$classId);
    }
    if($this->current_branch) {
      $this->db_readonly->where('c.branch_id',$this->current_branch);
    }
    if ($admission_type != 0) {
      $this->db_readonly->where('sy.admission_type',$admission_type);
    }
    $this->db_readonly->group_by('fss.id');
    $studentds = $this->db_readonly->get()->result();
    $schIds = [];
    foreach ($studentds as $key => $std) {
      array_push($schIds, $std->student_schedule_id);
    }
    if(!empty($schIds)){
      $components =  $this->db_readonly->select('fsi.fee_student_schedule_id, sum(fsic.component_amount) as component_amount, fsic.blueprint_component_id')
      ->from('feev2_student_installments fsi')
      ->join('feev2_student_installments_components fsic','fsi.id = fsic.fee_student_installment_id')
      ->where_in('fsi.fee_student_schedule_id', $schIds)
      ->group_by('fsic.blueprint_component_id, fsi.fee_student_schedule_id')
      ->get()->result();
      $feesdata = array();
      foreach ($components as $key => $fee) {
        $feesdata[$fee->fee_student_schedule_id][$fee->blueprint_component_id] = $fee->component_amount;
      }
      foreach ($studentds as $key => $val) {
        if (array_key_exists($val->student_schedule_id, $feesdata)) {
          $val->components = $feesdata[$val->student_schedule_id];
        }
      }
    }

    return $studentds;

  }

  public function get_fee_data_day_wise_summary($from_date, $to_date, $fee_type){
    $fromDate = date('Y-m-d',strtotime($from_date));
    $toDate = date('Y-m-d',strtotime($to_date));

    // Step 1: Get transaction data with schedule IDs
    $this->db_readonly->select('fss.feev2_cohort_student_id, sum(ft.amount_paid) as amount_paid, sum(ft.concession_amount) as concession_amount, sum(ft.fine_amount) as fine_amount');
    $this->db_readonly->from('feev2_transaction ft');
    $this->db_readonly->join('feev2_transaction_payment ftp', 'ft.id = ftp.fee_transaction_id');
    $this->db_readonly->join('feev2_student_schedule fss', 'ft.fee_student_schedule_id = fss.id');
    $this->db_readonly->join('feev2_cohort_student fcs', 'fss.feev2_cohort_student_id = fcs.id');
    $this->db_readonly->where('ft.status', 'SUCCESS');
    $this->db_readonly->where('ft.soft_delete !=', 1);

    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(ft.paid_datetime,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }

    if ($fee_type) {
      $this->db_readonly->where_in('fcs.blueprint_id',$fee_type);
    }

    $this->db_readonly->group_by('fss.feev2_cohort_student_id');
    $transaction_data = $this->db_readonly->get()->result_array();
    // Create a lookup array for cohort student IDs
    $cohort_student_data = array();
    foreach ($transaction_data as $tx) {
      $cohort_student_data[$tx['feev2_cohort_student_id']] = array(
        'amount_paid' => $tx['amount_paid'],
        'concession_amount' => $tx['concession_amount'],
        'fine_amount' => $tx['fine_amount']
      );
    }

    // Step 2: Get fee type data for these cohort student IDs
    $fee_type_data = array();
    if (!empty($cohort_student_data)) {
      $cohort_ids = array_keys($cohort_student_data);

      $this->db_readonly->select('fb.id, fb.name as fee_type, fcs.id as cohort_student_id');
      $this->db_readonly->from('feev2_cohort_student fcs');
      $this->db_readonly->join('feev2_blueprint fb', 'fcs.blueprint_id = fb.id');
      $this->db_readonly->where_in('fcs.id', $cohort_ids);
      $fee_types = $this->db_readonly->get()->result_array();

      // Step 3: Merge the data
      $merged_data = array();
      foreach ($fee_types as $ft) {
        $fee_type_id = $ft['id'];
        $fee_type_name = $ft['fee_type'];
        $cohort_id = $ft['cohort_student_id'];

        if (isset($cohort_student_data[$cohort_id])) {
          if (!isset($merged_data[$fee_type_id])) {
            $merged_data[$fee_type_id] = array(
              'id' => $fee_type_id,
              'fee_type' => $fee_type_name,
              'amount_paid' => 0,
              'concession_amount' => 0,
              'fine_amount' => 0
            );
          }

          $merged_data[$fee_type_id]['amount_paid'] += $cohort_student_data[$cohort_id]['amount_paid'];
          $merged_data[$fee_type_id]['concession_amount'] += $cohort_student_data[$cohort_id]['concession_amount'];
          $merged_data[$fee_type_id]['fine_amount'] += $cohort_student_data[$cohort_id]['fine_amount'];
        }
      }

      // Convert to indexed array and sort by fee_type
      $fee_type_data = array_values($merged_data);
      usort($fee_type_data, function($a, $b) {
        return strcmp($a['fee_type'], $b['fee_type']);
      });
    }

    $include_application = false;

    if (empty($fee_type) || in_array('application', $fee_type)) {
        $include_application = true;
    }

    if ($include_application) {
        $query="SELECT `af`.`id` as `transId`,  '0' as concession_amount, '0' as fine_amount, '-2' as id,  (case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amountPaid, (case when af.filled_by = 0 then opf.amount when af.filled_by !=0 then at.amount end) as amount_paid, '-1' as compId, 'Application' as fee_type
        FROM `admission_forms` `af`
        LEFT JOIN `admission_transaction` `at` ON `af`.`id`=`at`.`af_id` and `af`.`filled_by` !=0
        LEFT JOIN `online_application_fee_payment_master` `opf` ON `af`.`id`=`opf`.`source_id` and `opf`.`tx_response_code`=0
        LEFT JOIN `admission_status` `as` ON `af`.`id`=`as`.`af_id`
        WHERE `as`.`payment_status` = 'SUCCESS'
        AND `af`.`application_no` != ''
        AND date_format(af.created_on,'%Y-%m-%d') BETWEEN '".$fromDate."' and '".$toDate."'
        ORDER BY `af`.`application_no` ASC";

    $application_data = $this->db_readonly->query($query)->result_array();

    // Add application fees to the result
    if (!empty($application_data)) {
        $total_app_fee = 0;
        foreach ($application_data as $app) {
            $total_app_fee += floatval($app['amount_paid']);
        }

        $fee_type_data[] = array(
            'id' => '-2',
            'fee_type' => 'Application',
            'amount_paid' => $total_app_fee,
            'concession_amount' => 0,
            'fine_amount' => 0
        );
    }

  }

    // Step 5: Get sales transaction data
    // Only include sales data if no specific fee_type is selected, or if 'sales' is in the fee_type array
    $include_sales = false;

    // Check if we should include sales data
    if (empty($fee_type) || in_array('sales', $fee_type)) {
        $include_sales = true;
    }

    if ($include_sales) {
        $this->db_readonly->select("sum(st.amount) as amount_paid");
        $this->db_readonly->from('sales_master sm');
        $this->db_readonly->where('sm.soft_delete!=1');

    if ($fromDate && $toDate) {
      $this->db_readonly->where('date_format(sm.receipt_date,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
    }

    $this->db_readonly->join('sales_transactions st','sm.id=st.sales_master_id');
    $sales_data = $this->db_readonly->get()->row();

    // Add sales data to the result if there's any
    if (!empty($sales_data) && $sales_data->amount_paid > 0) {
        $fee_type_data[] = array(
            'id' => '-3',
            'fee_type' => 'Sales',
            'amount_paid' => $sales_data->amount_paid,
            'concession_amount' => 0,
            'fine_amount' => 0
        );
    }
  }

    // Final sort by fee_type
    usort($fee_type_data, function($a, $b) {
        return strcmp($a['fee_type'], $b['fee_type']);
    });


    return $fee_type_data;
  }

}


?>