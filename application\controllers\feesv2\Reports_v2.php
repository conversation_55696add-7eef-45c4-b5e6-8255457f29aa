<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  28 October 2019
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Reports Fees_structure
*/
class Reports_v2 extends CI_Controller {


  public $columnList = [
    [
      'dispaly_name'=>'Receipt Number',
      'column_name'=>'receipt_number as receipt_no',
      'var_name'=>'receipt_no',
    ],
    [
      'dispaly_name'=>'Paid date',
      'column_name'=>'date_format(ft.paid_datetime,"%d-%m-%Y") as paid_date',
      'var_name'=>'paid_date',
    ],

    [
      'dispaly_name'=> 'Student Name',
      'column_name' => "CONCAT(ifnull(sd.first_name,''),' ',ifnull(sd.last_name,'')) as student_name",
      'var_name'=>'student_name',

    ],

    [
      'dispaly_name'=>'Class',
      'column_name'=>"CONCAT(ifnull(c.class_name,''),' ',ifnull(cs.section_name,'')) as class_name",
      'var_name'=>'class_name',
    ],

    [
      'dispaly_name'=>'Concession',
      'column_name'=>'format(ft.concession_amount,2,"EN_IN") as concession',
      'var_name'=>'concession',
    ],

    [
      'dispaly_name'=>'Remarks',
      'column_name'=>'ftp.remarks',
      'var_name'=>'remarks',
    ],

    [
      'dispaly_name'=>'Paid Amount',
      'column_name'=>'format(ft.amount_paid,2,"EN_IN") as paid_amount',
      'var_name'=>'paid_amount',
    ],

    [
      'dispaly_name'=>'Payment Type',
      'column_name'=>'ftp.payment_type',
      'var_name'=>'payment_type',
    ],

    [
      'dispaly_name'=>'Fee Amount',
      'column_name'=>'',
      'var_name'=>'fee_amount',
    ],

    [
      'dispaly_name'=>'Installments',
      'column_name'=>'',
      'var_name'=>'installment',
    ],

    [
      'dispaly_name'=>'POS',
      'column_name'=>'ft.card_charge_amount as pos',
      'var_name'=>'pos',
    ],

    [
      'dispaly_name'=>'Fine Amount',
      'column_name'=>'ft.fine_amount as fine_amount',
      'var_name'=>'fine_amount',
    ],

    [
      'dispaly_name'=>'Discount Amount ',
      'column_name'=>'ft.discount_amount as discount_amount',
      'var_name'=>'discount_amount',
    ],

    [
      'dispaly_name'=>'Collected By',
      'column_name'=>'ft.collected_by as collected_by',
      'var_name'=>'collected_by',
    ],
    [
      'dispaly_name'=>'Components',
      'column_name'=>'',
      'var_name'=>'components',
    ],
    [
      'dispaly_name'=>'POS charges',
      'column_name'=>'ft.card_charge_amount as card_charge_amount',
      'var_name'=>'card_charge_amount',
    ],

    [
      'dispaly_name'=>'Refund Amount',
      'column_name'=>'ft.refund_amount as refund_amount',
      'var_name'=>'refund_amount',
    ],    

 ];  
  private $yearId;
	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('FEESV2')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/reports_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->helper('texting_helper');
    $this->load->helper('fees_helper');
    $this->load->model('avatar');
    $this->load->library('filemanager');
    $this->load->model('communication/emails_model', 'emails');
    $this->yearId = $this->acad_year->getAcadYearId();
  }

  public function student_wise_fees_details($fee_type ='', $clsId =''){
    $data['fee_type'] = $fee_type;
    $data['clsId'] = $clsId;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['combination'] = $this->Student_Model->getCombList();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['category'] = $this->settings->getSetting('category');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['refundAmount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['main_content'] = 'feesv2/reports/student_fees_summary';
    $this->load->view('inc/template', $data);
  }

  public function student_component_wise_fees_details($fee_type ='', $clsId =''){
    // $data['components'] = $this->fees_student_model->get_blueprint_components();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/component_wise_student_fees_summary';
    $this->load->view('inc/template', $data);
  }

  public function fees_component_detail($fee_type ='', $clsId =''){
    // $data['components'] = $this->fees_student_model->get_blueprint_components();
    $data['classes'] = $this->Student_Model->getClassNames();
    // $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // $data['admission_status'] = $this->settings->getSetting('admission_status');
    // $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/fees_component_detail';
    $this->load->view('inc/template', $data);
  }

  public function student_wise_component_fees_details(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['combination'] = $this->Student_Model->getCombList();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['category'] = $this->settings->getSetting('category');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['refundAmount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['main_content'] = 'feesv2/reports/student_wise_component_fees_summary';
    $this->load->view('inc/template', $data);
  }

  public function getStudentsForSummary_v2(){
    $selectedColumns = $this->input->post('selectedColumns');
    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $category = $this->input->post('category');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    $payment_status =$this->input->post('payment_status');
    $acad_year_id =$this->input->post('acad_year_id');
    $combination = $this->input->post('combination');
    $installment_type = $this->input->post('installment_type');
    $installmentId = $this->input->post('installmentId');
    $installmentId = $this->input->post('installmentId');
    $admission_status = $this->input->post('admission_status');
    $staff_kid = $this->input->post('staff_kid');
    $cohortStudentIds = $this->reports_model->get_fee_summary_student_wise_v2($payment_status, $fee_type, $clsId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId,$rte_nrteId, $acad_year_id,$combination, $installmentId, $admission_status, $staff_kid);
    $cohort_studentIds = array_chunk($cohortStudentIds, 150);
    echo json_encode($cohort_studentIds);
  }

  public function get_blueprint_components(){
    echo json_encode($this->fees_student_model->get_blueprint_components($_POST["fb_id"]));
  }

    public function get_blueprint_components1() {
    $fb_ids = $this->input->post("fb_id"); // This will be an array if multiple selected

    $components = [];
    if (is_array($fb_ids)) {
        foreach ($fb_ids as $fb_id) {
            $result = $this->fees_student_model->get_blueprint_components($fb_id);
            if (is_array($result)) {
                foreach ($result as $comp) {
                    // Avoid duplicates by component id
                    $components[$comp->id] = $comp;
                }
            }
        }
        // Return as a simple array
        $components = array_values($components);
    } else {
        $components = $this->fees_student_model->get_blueprint_components($fb_ids);
    }

    echo json_encode($components);
}

  public function component_wise_getStudentsForSummary(){
    $student_ids = $this->reports_model->component_wise_getStudentsForSummary($_POST);
    $cohort_student_ids = array_chunk($student_ids, 150);
    echo json_encode($cohort_student_ids);
  }

  public function get_component_wise_getStudentsForSummary(){
    $cohortstudentids =$_POST['cohortstudentids'];
    $component_id = $_POST['component_id'];
    $fee_type = $_POST['fee_type'];
    $fee_installment_id = $_POST['installmentId'];
    $result = $this->reports_model->get_component_wise_getStudentsForSummary_data($cohortstudentids, $component_id, $fee_type,$fee_installment_id);
    echo json_encode($result);
  }
  public function component_wise_getStudentsForSummary_v2(){
    $student_ids = $this->reports_model->component_wise_getStudentsForSummary_v2($_POST);
    $cohort_student_ids = array_chunk($student_ids, 150);
    echo json_encode($cohort_student_ids);
  }

  public function get_component_wise_getStudentsForSummary_v2(){
    $cohortstudentids =$_POST['cohortstudentids'];
    $component_id = $_POST['component_id'];
    $fee_type = $_POST['fee_type'];
    $fee_installment_id = $_POST['installmentId'];
    $result = $this->reports_model->get_component_wise_getStudentsForSummary_v2_data($cohortstudentids, $component_id, $fee_type,$fee_installment_id);
    echo json_encode($result);
  }

  public function student_wise_component_getStudentsForSummary(){
    $student_ids = $this->reports_model->student_wise_component_getStudentsForSummary($_POST);
    $cohort_student_ids = array_chunk($student_ids, 150);
    echo json_encode($cohort_student_ids);
  }

  public function get_student_wise_component_getStudentsForSummary(){
    $cohortstudentids = $_POST['cohortstudentids'];
    $selectedColumns = $this->input->post('selectedColumns');
    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $category = $this->input->post('category');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    $payment_status = $this->input->post('payment_status');
    $acad_year_id = $this->input->post('acad_year_id');
    $combination = $this->input->post('combination');
    $installment_type = $this->input->post('installment_type');
    $installmentId = $this->input->post('installmentId');
    $admission_status = $this->input->post('admission_status');
    $staff_kid = $this->input->post('staff_kid');

    $result = $this->reports_model->get_student_wise_component_getStudentsForSummary_data($cohortstudentids, $selectedColumns, $clsId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId, $rte_nrteId, $acad_year_id, $combination, $installmentId, $admission_status, $staff_kid, $fee_type);
    echo json_encode($result);
  }

  public function fees_collection_status(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    if (empty($fee_type_id)) {
      $fee_blueprints = $this->reports_model->get_blueprints_selection($data['fee_blueprints'][0]->id);
    }else{
      $fee_blueprints = $this->reports_model->get_blueprints_selection($fee_type_id);
    }
    $data['selected_blueprint'] = $fee_blueprints->id;
    $data['installment_types'] = $this->reports_model->get_installments_types($data['selected_blueprint']);
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/fee_collect_status';
    $this->load->view('inc/template', $data);
  }

  public function getStudentsForSummary_v2_details(){

    $cohortstudentids = $_POST['cohortstudentids'];
    $selectedColumns = $this->input->post('selectedColumns');
    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $category = $this->input->post('category');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    $payment_status =$this->input->post('payment_status');
    $rte_nrteId =$this->input->post('rte_nrteId');
    $acad_year_id =$this->input->post('acad_year_id');
    $combination = $this->input->post('combination');
    $installment_type = $this->input->post('installment_type');
    $installmentId = $this->input->post('installmentId');

    // echo "<pre>"; print_r($cohortstudentids); die();
    $result = $this->reports_model->get_fee_summary_student_wise_v2_details($cohortstudentids,$payment_status, $fee_type, $clsId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId,$rte_nrteId, $acad_year_id,$combination, $installmentId);
    // echo "<pre>"; print_r($result['headers']);
    // echo "<pre>"; print_r($result['summary']); die();
    echo json_encode($result);
  }

  /**
   * Enhanced method to get both regular and component-wise fee data
   * Supports backward compatibility with existing calls
   */
  public function getStudentsForSummary_v2_details_enhanced(){
    $cohortstudentids = $_POST['cohortstudentids'];
    $selectedColumns = $this->input->post('selectedColumns');
    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $category = $this->input->post('category');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    $payment_status =$this->input->post('payment_status');
    $acad_year_id =$this->input->post('acad_year_id');
    $combination = $this->input->post('combination');
    $installment_type = $this->input->post('installment_type');
    $installmentId = $this->input->post('installmentId');
    $admission_status = $this->input->post('admission_status');
    $staff_kid = $this->input->post('staff_kid');
    $transaction_hide_show = $this->input->post('transaction_hide_show');

    // New parameters for component data
    $include_components = $this->input->post('include_components') ?: 'no';
    $component_ids = $this->input->post('component_ids');

    $result = $this->reports_model->get_fee_summary_student_wise_v2_details_enhanced(
      $cohortstudentids,
      $payment_status,
      $fee_type,
      $clsId,
      $admission_type,
      $mediumId,
      $category,
      $donorsId,
      $created_byId,
      $classSectionId,
      $rte_nrteId,
      $acad_year_id,
      $combination,
      $installmentId,
      $transaction_hide_show,
      $include_components,
      $component_ids
    );

    echo json_encode($result);
  }

  public function getStudentsForSummary_v2_details_new(){
    $cohortstudentids = $_POST['cohortstudentids'];
    $selectedColumns = $this->input->post('selectedColumns');
    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $admission_type = $this->input->post('admission_type');
    $paymentModes = $this->input->post('paymentModes');
    $fee_type = $this->input->post('fee_type');
    $mediumId = $this->input->post('mediumId');
    $category = $this->input->post('category');
    $donorsId = $this->input->post('donorsId');
    $rte_nrteId = $this->input->post('rte_nrteId');
    $created_byId = $this->input->post('created_byId');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $stopId = $this->input->post('stopId');
    $routeId = $this->input->post('routeId');
    $itemId = $this->input->post('itemId');
    $medium = $this->settings->getSetting('medium');
    $payment_status =$this->input->post('payment_status');
    $acad_year_id =$this->input->post('acad_year_id');
    $combination = $this->input->post('combination');
    $installment_type = $this->input->post('installment_type');
    $installmentId = $this->input->post('installmentId');
    $admission_status = $this->input->post('admission_status');
    $staff_kid = $this->input->post('staff_kid');
    $transaction_hide_show = $this->input->post('transaction_hide_show');

    // NEW: Check if component-wise data is requested
    $show_component_wise = $this->input->post('show_component_wise');

    // echo "<pre>"; print_r($cohortstudentids); die();
    $result = $this->reports_model->get_fee_summary_student_wise_v2_details_new($cohortstudentids,$payment_status, $fee_type, $clsId, $admission_type, $mediumId, $category, $donorsId, $created_byId, $classSectionId,$rte_nrteId, $acad_year_id,$combination, $installmentId, $transaction_hide_show, $show_component_wise);
    // echo "<pre>"; print_r($result['headers']);
    // echo "<pre>"; print_r($result['summary']); die();
    echo json_encode($result);
  }

  public function get_fee_history_data(){
    $stdId = $_POST['stdId'];
    $result = $this->reports_model->fee_history_data_by_stdbyId($stdId);
    echo json_encode($result);
  }

  public function search_fee_receipt_number(){
    $data['all_names'] = $this->reports_model->get_student_names_all();
    $data['main_content'] = 'feesv2/reports/receipt_search';
    $this->load->view('inc/template', $data);
  }

  public function get_student_fee_history(){
    $student_id = $_POST['student_id'];
    $admissionNo = $_POST['admissionNo'];
    if (!empty($admissionNo)) {
      $student_id = $this->reports_model->get_admission_number_wise_stdId($admissionNo);
    }
    $data['student_data'] = $this->reports_model->fee_history_data_by_stdbyId($student_id);
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');
    $data['std_id'] = $student_id;
    $fee_blueprints = $this->fees_student_model->get_blueprints();
    // $acad_year_id = $this->acad_year->getAcadYearId();
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      // $acad_year_id = $fbp->acad_year_id;
      $fbp->std_fee = $this->fees_collection_model->fee_student_fee_details($student_id, $fbp->id);
      $fbp->compCount = $this->fees_collection_model->fee_total_no_of_components($fbp->id);
      if (!empty($fbp->std_fee)) {
        $fbp->installments = $this->fees_collection_model->get_installments_all_history($fbp->std_fee->feev2_blueprint_installment_types_id);
        $fbp->history = $this->fees_collection_model->fee_student_fee_history($fbp->std_fee->stdSchId);
      }
    }
    // $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $acad_year_id);
    $data['fee_history'] = $fee_blueprints;
    $data['delete_authorization'] = $this->authorization->isAuthorized('FEESV2.SOFT_DELETE_RECEIPTS');
    echo json_encode($data);
  }

  public function get_print_receipt(){
    $fee_receipt_number = $this->input->post('fee_receipt_number');
    $data['print_receipt'] = $this->reports_model->get_print_receiptbynumber($fee_receipt_number);
    $data['main_content'] = 'feesv2/reports/receipt_search';
    $this->load->view('inc/template', $data);
  }

  public function balance_report($fee_type=''){
    $data['fee_type'] = $fee_type;
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['email_template'] = $this->reports_model->get_email_template_for_fee();
    $data['sms_template'] = $this->reports_model->get_sms_template_for_fee();
    $data['email_option'] = $this->settings->getSetting('fee_balance_from_email');
    $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['combinationList'] = $this->Student_Model->getCombinations();
    $data['student_names'] = $this->reports_model->get_fees_balance_student_names();
    $data['adSelected'] = ['1','2']; // 2 is default select admission status approved
    if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'feesv2/reports/balance_sms_mobile';
    }else{
      $data['main_content'] = 'feesv2/reports/balance_sms';     
    }
    $this->load->view('inc/template', $data);
  }

  public function get_sms_template_content_for_balance_fee(){
    $templateId = $_POST['templateId'];
    $result =  $this->reports_model->get_sms_content_for_balancebyid($templateId);
    echo json_encode($result);
  }

  public function send_balance_sms(){
    $input = $this->input->post();
    if ($input['communication_type'] == 'email') {
      $this->_send_fee_balance_email($input);
    }else{
      $arraymsg = array_values($input['std_id']);
      $sent_by = $this->authorization->getAvatarId();
      $input_arr = array();
      $this->load->helper('texting_helper');
      $input_arr['student_id_messages'] = $input['std_id'];
      $input_arr['source'] = 'Fee Balance';
      $text_send_to = $input['sent_to'];
      $input_arr['mode'] = $input['communication_type'];
     
      $input_arr['send_to'] = $text_send_to;
      $father = sendUniqueText($input_arr);
      if($father['success'] != ''){
        $insId1 = 1;
      } else {
        $insId1 = 0;
      }

      if($insId1 == 1) {
        $this->session->set_flashdata('flashSuccess', 'SMS Sent Successfully');
      } else{
        $this->session->set_flashdata('flashSuccess', 'Something went wrong.');
      }
      redirect('feesv2/reports_v2/balance_report');
    }
  
  }

  public function _send_fee_balance_email($input){
    $email = $this->reports_model->get_fee_balance_email_templatebyId($input['emailTemplate']);    
    if (!empty($email)) {
      $stakeholder_ids = array_keys($input['std_id']);  
      $sendTo = $input['sent_to']; 
      foreach ($input['std_id'] as $stdId => $message) {
        $members = [];
        foreach ($input['email'][$stdId] as $email_id) {
           $members[] = array(
            'message' => $message,
            'email' => $email_id
           ); 
        }
        
        $master_data = array(
          'subject' => $email->email_subject,
          'body' => $message,
          'source' => 'Fee Balance Email UI',
          'sent_by' => $this->authorization->getAvatarId(),
          'recievers' => 'Students',
          'from_email' => $email->registered_email,
          'files' => '',
          'acad_year_id' => $this->yearId,
          'visible' => 1,
          'sender_list' => NULL,
          'sending_status' => 'Initiated'
        );
        $master_id = $this->emails->saveEmail($master_data);      
        $sent_data = array();
        if ($master_id) {
          $sent_data = $this->reports_model->getStudents_email_fee_balance($stakeholder_ids, $sendTo);
        }
        $status = $this->emails->save_sending_data($sent_data, $master_id);
        if ($status) {
          $this->load->helper('email_helper');
          $email_response = sendEmail('No-Common-Message', $email->email_subject, $master_id, $members, $email->registered_email);
        }
      }
    }
    $this->session->set_flashdata('flashSuccess', 'Email Sent Successfully');
    redirect('feesv2/reports_v2/balance_report');
  }

  public function get_fee_balance_email_content(){
    $emailtemplateId = $this->input->post('emailtemplateId');
    $result = $this->reports_model->get_fee_balance_email_templatebyId($emailtemplateId);
    echo json_encode($result);
  }

  public function get_fee_balance_student_count(){
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $installmentId = $_POST['installmentId'];
    $installment_type = $_POST['installment_type'];
    $fee_type = $_POST['fee_type'];
    $staff_kid = $_POST['staff_kid'];
    $rte_nrteId = $_POST['rte_nrteId'];
    $payment_status = $_POST['payment_status'];
    $donorsId = $_POST['donorsId'];
    $combination = $_POST['combination'];
    $show_over_due = $_POST['show_over_due'];
    $admissionStatus = $_POST['admissionStatus'];
    $stakeholder_id = isset($_POST['stakeholder_id']) ? $_POST['stakeholder_id'] : 0 ;
    $studentIds = $this->reports_model->get_balance_sms_student_count($classSectionId, $installmentId, $fee_type, $installment_type, $staff_kid, $classId, $rte_nrteId, $payment_status, $donorsId, $combination, $show_over_due,$stakeholder_id, $admissionStatus);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_fee_balance_details(){
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $installmentId = $_POST['installmentId'];
    $installment_type = $_POST['installment_type'];
    $fee_type = $_POST['fee_type'];
    $student_ids = $_POST['student_ids'];
    $staff_kid = $_POST['staff_kid'];
    $payment_status = $_POST['payment_status'];    
    $donorsId = $_POST['donorsId'];
    $show_over_due = $_POST['show_over_due'];
    $result = $this->reports_model->get_fee_balance_student_list($student_ids, $classSectionId, $installmentId, $fee_type, $installment_type,$staff_kid, $classId, $payment_status, $donorsId, $show_over_due);
    echo json_encode($result);
  }

  public function get_fee_balance_details_summary(){
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $installmentId = $_POST['installmentId'];
    $installment_type = $_POST['installment_type'];
    $fee_type = $_POST['fee_type'];
    $student_ids = $_POST['student_ids'];
    $staff_kid = $_POST['staff_kid'];
    $payment_status = $_POST['payment_status'];    
    $donorsId = $_POST['donorsId'];
    $result = $this->reports_model->get_fee_balance_student_list_summary($student_ids, $classSectionId, $installmentId, $fee_type, $installment_type,$staff_kid, $classId, $payment_status, $donorsId);
    echo json_encode($result);
  }

  public function get_fee_balance_sms_details(){
    $classSectionId = $_POST['classSectionId'];
    $installmentId = $_POST['installmentId'];
    $installment_type = $_POST['installment_type'];
    $fee_type = $_POST['fee_type'];
    $student_ids = $_POST['student_ids'];
    $show_over_due = $_POST['show_over_due'];
    $result = $this->reports_model->get_fee_balance_student_list_sms($student_ids, $installmentId, $fee_type, $show_over_due);
    echo json_encode($result);
  }

  public function get_student_fee_history_search(){
    $student_id = $_POST['student_id'];
    $admissionNo = $_POST['admissionNo'];
    if (!empty($admissionNo)) {
      $student_id = $this->reports_model->get_admission_number_wise_stdId($admissionNo);
    }
    $data['excess_amount'] = $this->fees_collection_model->get_over_all_additional_amount($student_id);
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');

    // $data['fee_data'] = $this->reports_model->fee_student_history_data_by_stdbyId($student_id);
    $data['fee_data'] = $this->reports_model->fee_student_history_data_by_stdbyId_v2($student_id);
    
    $fee_blueprints = $this->fees_student_model->get_blueprintsv1();
    // $acad_year_id = $this->acad_year->getAcadYearId();
    foreach ($fee_blueprints as &$fbp) {
      //Assign cohorts if not assigned yet
      // $acad_year_id = $fbp->acad_year_id;
      $fbp->std_fee = $this->fees_collection_model->fee_student_fee_details($student_id, $fbp->id);
      $fbp->compCount = $this->fees_collection_model->fee_total_no_of_components($fbp->id);
      $fbp->concession_adjustment = $this->fees_student_model->get_concession_adjustment_amount($student_id, $fbp->id);
      if (!empty($fbp->std_fee)) {
        $fbp->installments = $this->fees_collection_model->get_installments_all_history($fbp->std_fee->feev2_blueprint_installment_types_id);
        $fbp->history = $this->fees_collection_model->fee_student_fee_history($fbp->std_fee->stdSchId);
      }
    }
   
    $data['refund'] = $this->authorization->isAuthorized('FEESV2.REFUND');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['fee_refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['delete_authorization'] = $this->authorization->isAuthorized('FEESV2.SOFT_DELETE_RECEIPTS');
    // $data['student'] = $this->fees_student_model->get_std_detailsbyId($std_id, $acad_year_id);
    $data['fee_history'] = $fee_blueprints;
    echo json_encode($data);
  }

  public function get_installment_details_by_schId(){
    $schId = $_POST['schId'];
    $result = $this->reports_model->get_installments_detailsbyShcId($schId);
    echo json_encode($result);
  }

  public function management_summary($admission_status = null){
    $admStatus = $this->input->post('admission_status');
  
    if(empty($admStatus)){
      $admStatus = ['2'];
    }
    $data['adSelected'] = $admStatus;
    $data['fee_management'] = $this->reports_model->get_fee_management_summary($admStatus);
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['fee_refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['admissionStatus'] = $this->settings->getSetting('admission_status');
    //echo "<pre>"; print_r($data['admissionStatus']);die();

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'feesv2/reports/management_summary_mobile';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'feesv2/reports/management_summary_mobile';
    } else {
      $data['main_content'] = 'feesv2/reports/management_summary';
    }
    $this->load->view('inc/template', $data);
  }

  public function fee_audit(){
    $data['fee_amount'] = $this->reports_model->get_total_fees_all_table();
    
    $data['payment_issue'] = $this->reports_model->get_payment_issue_studentwise();
    $data['payment_issue_ins'] = $this->reports_model->get_payment_issue_ins_studentwise();
    $data['payment_issue_comp'] = $this->reports_model->get_payment_issue_comp_studentwise();
    $data['receipt'] = $this->reports_model->get_check_receipt_number();
    $data['main_content'] = 'feesv2/reports/fee_audit';
    $this->load->view('inc/template', $data);
  }

  public function adjustment_report(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/adjustment';
    $this->load->view('inc/template', $data);
  }

  public function fine_report(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/fine';
    $this->load->view('inc/template', $data);
  }
  public function fine_waiver_report(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/fine_waiver';
    $this->load->view('inc/template', $data);
  }

  public function excess_report(){
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/excess_fee_report';
    $this->load->view('inc/template', $data);
  }

  public function get_excess_amount_details() {
    $class_id = $_POST['classId'];
    $excess_payment_type = $_POST['excess_payment_type'];
    $excess_amount_details = $this->reports_model->get_excess_amount_details($class_id, $excess_payment_type);
    echo json_encode($excess_amount_details);
  }

  public function get_fee_adjustment_student_count(){
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $fee_type = $_POST['fee_type'];
    $studentIds = $this->reports_model->get_adjustment_student_count($classSectionId,$fee_type,$classId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_fee_fine_student_count(){
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $fee_type = $_POST['fee_type'];
    $studentIds = $this->reports_model->get_fine_student_count($classSectionId,$fee_type,$classId);
    $studentId = array_chunk($studentIds, 100);
    echo json_encode($studentId);
  }

  public function get_fee_fine_waiver_student(){
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $fee_type = $_POST['fee_type'];
    $students_fine = $this->reports_model->get_fine_waiver_student($classSectionId,$fee_type,$classId);
    echo json_encode($students_fine);
  }

  public function get_fee_adjustment_details(){
    $student_ids = $_POST['student_ids'];
    $fee_type = $_POST['fee_type'];
    $classSectionId = $_POST['classSectionId'];
    $classId = $_POST['classId'];
    $result = $this->reports_model->get_adjustment_list_new($student_ids, $fee_type, $classSectionId, $classId);
    echo json_encode($result);
  }

  public function get_fee_fine_details(){
    $student_ids = $_POST['student_ids'];
    $fee_type = $_POST['fee_type'];
    $classSectionId = $_POST['classSectionId'];
    $classId = $_POST['classId'];
    $result = $this->reports_model->get_fine_list_new($student_ids, $fee_type, $classSectionId, $classId);
    echo json_encode($result);
  }

  public function fee_paid_amount_edit($blueprint_id = 0, $student_id = 0){
    if ($student_id !='0') {
      $data['fee_amount'] = $this->reports_model->get_fee_paid_amount_details($blueprint_id, $student_id);
    }else{
       $data['fee_amount'] = [];
    }
    $data['main_content'] = 'feesv2/paid_amount_edit';
    $this->load->view('inc/template', $data);
  }

  public function student_wise_fees_summary_details(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    // $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // $data['combination'] = $this->Student_Model->getCombList();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    // $data['rteType'] = $this->settings->getSetting('rte');
    // $data['category'] = $this->settings->getSetting('category');
    // $data['donors'] = $this->Student_Model->getDonorList();

    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    // $data['fee_data'] = $this->reports_model->get_fees_summary_data();
    $data['main_content'] = 'feesv2/reports/student_fees_summary_overall';
    $this->load->view('inc/template', $data);
  }

  public function student_wise_fees_summary_details_v2(){
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // $data['combination'] = $this->Student_Model->getCombList();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    // $data['category'] = $this->settings->getSetting('category');
    // $data['donors'] = $this->Student_Model->getDonorList();

    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    // $data['fee_data'] = $this->reports_model->get_fees_summary_data();
    $data['main_content'] = 'feesv2/reports/student_fees_summary_overall_v2';
    $this->load->view('inc/template', $data);
  }

    public function get_student_summary_details_v2(){
    $clsId = $this->input->post('clsId');
    $classSectionId = $this->input->post('classSectionId');
    $fee_type = $this->input->post('fee_type');
    $admission_type = $this->input->post('admission_type');
    $payment_status = $this->input->post('payment_status');
    $classSectionId = $this->input->post('classSectionId');
    $acad_year_id = $this->input->post('acad_year_id');
    $admission_status = $this->input->post('admission_status');
    $staff_kid = $this->input->post('staff_kid');
    $rte_nrteId = $this->input->post('rte_nrteId');

    // handling new parameters
    $cohortStudentIds = $this->reports_model->get_fee_details_summary_student_v2(
      $clsId,
      $fee_type,
      $admission_type,
      $payment_status,
      $classSectionId,
      $acad_year_id,
      $admission_status,
      $staff_kid,
      $rte_nrteId, 
      $classSectionId
    );

    $cohort_studentIds = array_chunk($cohortStudentIds, 150);
    echo json_encode($cohort_studentIds);
  }

    public function get_student_summary_fee_data_v2(){
    $cohortstudentids = $this->input->post('cohortstudentids');
    $fee_type = $this->input->post('fee_type');
    $payment_status = $this->input->post('payment_status');
    $fees_data = $this->reports_model->get_student_summary_fee_data_details_v2($cohortstudentids,$fee_type, $payment_status);
    echo json_encode($fees_data);
  }

  public function get_student_summary_details(){
    $clsId = $this->input->post('clsId');
    $fee_type = $this->input->post('fee_type');
    $admission_type = $this->input->post('admission_type');
    $payment_status = $this->input->post('payment_status');
    $cohortStudentIds = $this->reports_model->get_fee_details_summary_student($clsId, $fee_type, $admission_type, $payment_status);
    $cohort_studentIds = array_chunk($cohortStudentIds, 150);
    echo json_encode($cohort_studentIds);
  }

  public function get_student_summary_fee_data(){
    $cohortstudentids = $this->input->post('cohortstudentids');
    $fee_type = $this->input->post('fee_type');
    $payment_status = $this->input->post('payment_status');
    $fees_data = $this->reports_model->get_student_summary_fee_data_details($cohortstudentids,$fee_type, $payment_status);
    echo json_encode($fees_data);
  }

  public function get_fees_edit_history_data(){
    $result = $this->reports_model->get_fees_edit_history_data($_POST['from_date'],$_POST['to_date'],$_POST['name']);
    echo json_encode($result);
  }

  public function get_class_section_by_fees_selection_class(){
    $feeclass = $_POST['feeclass'];
    $result = $this->reports_model->get_class_section_by_fees_selection_classbyId($feeclass);
    echo json_encode($result);
  }

  public function fees_distrubance_student_details(){
    $result = $this->reports_model->get_fees_distrubance_student_details();
    echo json_encode($result);
  }

  public function management_summary_date_wise(){
    $data['fee_blueprints'] = $this->fees_student_model->get_all_blueprints_collection_summary();
    $data['additionalAmount'] = $this->reports_model->get_payment_options_additional_amount();
    $data['sales'] = $this->authorization->isModuleEnabled('SALES');
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/management_day_wise_summary';
    $this->load->view('inc/template', $data);
  }

  public function get_fee_day_wise_summary(){
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $fee_type = $this->input->post('fee_type');
    $result = $this->reports_model->get_fee_data_day_wise_summary($from_date, $to_date, $fee_type);
    echo json_encode($result);
  }

}