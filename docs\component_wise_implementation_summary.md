# Component-wise Student Fees Summary Implementation

## Overview
This implementation adds component-wise data display functionality to the student fees summary report. When the "Show Component Wise" checkbox is checked, the report displays data with installment and component name columns instead of regular installment columns.

## Changes Made

### 1. Controller Changes (application/controllers/feesv2/Reports_v2.php)
- **Method**: `getStudentsForSummary_v2_details_new()`
- **Changes**: 
  - Added `show_component_wise` parameter capture from POST data
  - Passed the parameter to the model method

```php
// NEW: Check if component-wise data is requested
$show_component_wise = $this->input->post('show_component_wise');

$result = $this->reports_model->get_fee_summary_student_wise_v2_details_new(
    // ... existing parameters ...,
    $show_component_wise
);
```

### 2. Model Changes (application/models/feesv2/Reports_model.php)
- **Method**: `get_fee_summary_student_wise_v2_details_new()`
- **Changes**:
  - Added `$show_component_wise = false` parameter to method signature
  - Modified component data fetching to be conditional based on the parameter
  - Updated header generation logic to show component-wise headers when requested
  - Added `show_component_wise` flag to return array

#### Key Features:
- **Conditional Component Data Fetching**: Only fetches component data when `show_component_wise` is true
- **Dynamic Header Generation**: 
  - Regular mode: Shows installment headers (e.g., "Installment 1 - Tuition Fee")
  - Component mode: Shows component headers (e.g., "Installment 1 - Admission Fee")
- **Component Headers Structure**: Each component gets 4 columns:
  - Total Amount
  - Amount Paid  
  - Concession
  - Balance

### 3. View Changes (application/views/feesv2/reports/student_fees_summary.php)
- **Changes**:
  - Added `show_component_wise` parameter to AJAX request
  - Created new `constructComponentWiseFeeReport()` function
  - Modified success callback to conditionally call component-wise or regular report construction

#### New Function: `constructComponentWiseFeeReport()`
- Displays student data with component-wise breakdown
- Shows installment name + component name data
- Handles missing component data gracefully (shows "-")
- Maintains all existing student information columns

## Data Structure

### Component Data Format
The component data is organized as:
```javascript
{
  component_headers: {
    "Installment 1 - Admission Fee": "Installment 1 - Admission Fee",
    "Installment 1 - Tuition Fee": "Installment 1 - Tuition Fee",
    "Installment 2 - Tuition Fee": "Installment 2 - Tuition Fee"
  },
  feeArray: {
    student_id: {
      "Installment 1 - Admission Fee": {
        component_amount: 5000,
        component_amount_paid: 3000,
        concession_amount: 500,
        balance_amount: 1500
      }
    }
  }
}
```

## Usage Instructions

### For Users:
1. Navigate to Fee Dashboard → Fee Detail Report
2. Set your desired filters (Fee Type, Class, etc.)
3. Check the "Show Component Wise" checkbox
4. Click "Get Report"
5. The report will display with component-wise columns showing:
   - Each installment-component combination as separate columns
   - Total Amount, Amount Paid, Concession, and Balance for each component

### For Developers:
The implementation is backward compatible:
- Unchecked checkbox: Works exactly as before (regular installment view)
- Checked checkbox: Shows component-wise view with installment + component name columns

## Technical Details

### Database Queries
- **Regular Mode**: Fetches installment-level data only
- **Component Mode**: Additionally fetches component-level data with joins to:
  - `feev2_student_installments_components`
  - `feev2_blueprint_components`
  - Groups by student, component, and installment

### Performance Considerations
- Component data is only fetched when requested (conditional loading)
- Uses existing database indexes
- Maintains the same chunked processing approach (150 students per batch)

## Testing Checklist

### Basic Functionality:
- [ ] Checkbox toggles between regular and component-wise views
- [ ] Component-wise view shows installment + component name headers
- [ ] Data displays correctly for students with component assignments
- [ ] Missing component data shows "-" instead of errors
- [ ] All existing filters work with component-wise view

### Data Accuracy:
- [ ] Component amounts match individual component records
- [ ] Totals are calculated correctly
- [ ] Concession and balance amounts are accurate
- [ ] Student information columns remain unchanged

### UI/UX:
- [ ] Headers are properly formatted and readable
- [ ] Table scrolling works correctly with additional columns
- [ ] Print and Excel export functions work
- [ ] Loading indicators display properly

## Future Enhancements
- Add component-wise summary totals
- Include component-wise transaction details
- Add component filtering options
- Implement component-wise Excel export formatting
